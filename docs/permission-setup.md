# Permission & Role-Based Access Control System

## Overview

This documentation explains the comprehensive role-based permission system implemented in the FinWage application. The system provides granular access control for different user roles with automatic UI adaptation and multi-layer security.

## System Architecture

### 1. Core Components

#### Permission Types

```typescript
export type Permission =
  | 'dashboard.view'
  | 'employers.view'
  | 'employees.view'
  | 'employees.delete'
  | 'repayments.view'
  | 'salary_requests.approve';
// ... and more
```

#### Role Definitions

```typescript
export type Role = 'super_admin' | 'employer' | 'sub_admin';
```

#### Role-Permission Mapping

```typescript
export const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  super_admin: [
    /* admin-specific permissions */
  ],
  employer: [
    /* full employer permissions */
  ],
  sub_admin: [
    /* limited employer permissions */
  ],
};
```

### 2. Permission Manager (Singleton Pattern)

The `PermissionManager` class is the core of the permission system:

```typescript
export class PermissionManager {
  private static instance: PermissionManager;
  private userRole: Role | null = null;
  private userPermissions: Permission[] = [];

  // Key methods:
  setUserRole(role: Role); // Initialize user permissions
  hasPermission(permission: Permission): boolean; // Check single permission
  getFilteredNavItems(navItems: NavItem[]): NavItem[]; // Filter navigation
  canAccessRoute(requiredPermission: Permission, allowedRoles?: Role[]): boolean;
}
```

## Implementation Flow

### Step 1: Authentication & Role Assignment

**Location**: `src/modules/auth/login/login-page.tsx`

1. User logs in through authentication system
2. Backend returns user role information
3. Frontend maps backend roles to internal `Role` type
4. Role is stored in auth store and localStorage

### Step 2: Permission Initialization

**Location**: `src/providers/UserRoleProvider.tsx`

1. Provider component wraps the entire application
2. Retrieves user role from auth store
3. Initializes `PermissionManager` singleton with user role
4. Loads corresponding permissions array for the role

### Step 3: Route Protection

**Location**: `src/components/auth/auth-guard.tsx`

1. `AuthGuard` component protects routes at high level
2. Prevents role mismatches (e.g., super_admin accessing /employer routes)
3. Redirects users to appropriate dashboards based on role
4. Provides fallback for unauthorized access

### Step 4: Navigation Filtering

**Location**: `src/components/layout/employer-layout.tsx`

The selected code demonstrates navigation filtering:

```typescript
const filteredNavItems = permissionManager.getFilteredNavItems(EMPLOYER_NAV_ITEMS);
```

**How it works:**

1. `EmployerSidebar` component gets `PermissionManager` instance
2. Calls `getFilteredNavItems()` with role-specific navigation items
3. Method filters items based on user's permissions
4. Only items user has permission for are rendered in sidebar

**Navigation Item Structure:**

```typescript
interface NavItem {
  title: string;
  url: string;
  icon: any;
  key: string;
  requiredPermission: Permission;
  roles?: Role[]; // Optional: specific roles only
}
```

### Step 5: Page-Level Permission Checks

**Location**: Individual page components (e.g., `src/app/(main)/employer/repayments/page.tsx`)

```typescript
export default function RepaymentsPage() {
  const { hasPermission } = usePermissions();

  if (!hasPermission('repayments.view')) {
    return <AccessDeniedComponent />;
  }

  return (
    <AuthGuard allowedRoles={['employer', 'sub_admin']}>
      <RepaymentManagement />
    </AuthGuard>
  );
}
```

**Protection Layers:**

1. **Route Guard**: `AuthGuard` checks allowed roles
2. **Permission Check**: `hasPermission()` validates specific permission
3. **Component Level**: Individual components check permissions for actions

### Step 6: Component-Level Permission Checks

**Location**: Individual components (e.g., `src/components/repayment-management.tsx`)

```typescript
{hasPermission('repayments.export') && (
  <Button onClick={handleExportReport}>
    <Download className="mr-2 h-4 w-4" />
    Export Report
  </Button>
)}
```

## Role-Specific Configurations

### Super Admin

- **Access**: Admin dashboard only (`/admin/*` routes)
- **Permissions**: Employer management, approvals, analytics, system settings
- **Restrictions**: Cannot access employer-specific features

### Employer

- **Access**: Employer dashboard (`/employer/*` routes)
- **Permissions**: Full employee management, salary requests, wages, repayments
- **Features**: Complete CRUD operations, reporting, role management

### Sub Admin

- **Access**: Employer dashboard (`/employer/*` routes)
- **Permissions**: Limited subset of employer permissions
- **Restrictions**: Configurable based on business requirements

## Customizing Permissions

### Removing Repayments Access for Sub-Admin

1. **Update Role Permissions:**

```typescript
export const ROLE_PERMISSIONS: Record<Role, Permission[]> = {
  sub_admin: [
    'dashboard.view',
    'employees.view',
    // Remove: 'repayments.view', 'repayments.view_details', 'repayments.export'
    // ... other permissions
  ],
};
```

2. **Create Separate Navigation:**

```typescript
export const SUB_ADMIN_NAV_ITEMS: NavItem[] = [
  // Include only allowed navigation items
  // Exclude repayments navigation item
];
```

3. **Update Navigation Logic:**

```typescript
getNavItemsForRole(): NavItem[] {
  switch (this.userRole) {
    case 'sub_admin':
      return this.getFilteredNavItems(SUB_ADMIN_NAV_ITEMS);
    // ... other cases
  }
}
```

### Removing Employee Delete Permission

1. **Update Permissions Array:**

```typescript
sub_admin: [
  'employees.view',
  'employees.create',
  'employees.edit',
  // Remove: 'employees.delete'
];
```

2. **Component automatically adapts:**

```typescript
{hasPermission('employees.delete') && (
  <Button variant="destructive">Delete Employee</Button>
)}
```

## Security Features

### Multi-Layer Protection

1. **Route Level**: AuthGuard prevents unauthorized route access
2. **Page Level**: Permission checks before component rendering
3. **Component Level**: Feature-specific permission validation
4. **API Level**: Backend validates permissions (recommended)

### Type Safety

- TypeScript ensures permission strings are valid
- Compile-time checking prevents typos
- IntelliSense support for permission names

### Centralized Management

- All permissions defined in single location
- Easy to audit and modify
- Consistent across application

## Best Practices

### 1. Granular Permissions

- Use specific permissions like `employees.delete` vs generic `employees.manage`
- Allows fine-grained control over features

### 2. Defensive Programming

- Always check permissions before rendering sensitive components
- Provide fallback UI for unauthorized access
- Handle edge cases gracefully

### 3. Performance Considerations

- `PermissionManager` uses singleton pattern for efficiency
- Permissions loaded once per session
- Navigation filtering happens at render time

### 4. Maintainability

- Keep permission logic in centralized location
- Use descriptive permission names
- Document role-specific behaviors

## Troubleshooting

### Common Issues

1. **Navigation items not appearing:**
   - Check if user has required permission
   - Verify role is set correctly in PermissionManager
   - Ensure navigation item has correct `requiredPermission`

2. **Access denied on valid routes:**
   - Verify AuthGuard `allowedRoles` includes user's role
   - Check if permission is included in role's permission array
   - Ensure PermissionManager is initialized with correct role

3. **Permissions not updating:**
   - PermissionManager is singleton - may need to reinitialize
   - Check if role change triggers permission reload
   - Verify localStorage/auth store is updated

### Debugging Tips

1. **Check Current Role:**

```typescript
const permissionManager = PermissionManager.getInstance();
console.log('Current role:', permissionManager.getUserRole());
```

2. **Check Specific Permission:**

```typescript
console.log('Has permission:', permissionManager.hasPermission('repayments.view'));
```

3. **Check Filtered Navigation:**

```typescript
const filteredItems = permissionManager.getFilteredNavItems(EMPLOYER_NAV_ITEMS);
console.log('Filtered nav items:', filteredItems);
```

## Future Enhancements

### Dynamic Permissions

- Load permissions from backend API
- Support for user-specific permission overrides
- Real-time permission updates

### Custom Roles

- Support for organization-defined roles
- Role inheritance and composition
- Temporary permission grants

### Audit Logging

- Track permission checks and access attempts
- Monitor unauthorized access attempts
- Generate compliance reports

## File Structure

```
src/
├── lib/
│   └── permissions.ts          # Core permission system
├── components/
│   ├── auth/
│   │   └── auth-guard.tsx      # Route protection
│   └── layout/
│       └── employer-layout.tsx # Navigation filtering
├── providers/
│   └── UserRoleProvider.tsx    # Permission initialization
├── hooks/
│   └── usePermissions.ts       # Permission hook
└── app/
    └── (main)/
        └── employer/           # Protected routes
```

This permission system provides a robust, scalable foundation for role-based access control while maintaining flexibility for future requirements.
