import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

export type ChangePasswordPayload = {
  currentPassword: string;
  newPassword: string;
};

export interface ChangePasswordResponse {
  statusCode: number;
  message: string;
  data: any;
  timestamp: string;
  path: string;
  method: string;
}

const changePasswordApi = async (
  payload: ChangePasswordPayload
): Promise<ChangePasswordResponse> => {
  const response = await apiClient.post('/auth/change-password', payload);
  return response.data;
};

export const useChangePassword = () => {
  return useMutation({
    mutationFn: changePasswordApi,
  });
};
