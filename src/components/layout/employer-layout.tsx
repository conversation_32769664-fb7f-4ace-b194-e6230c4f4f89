'use client';

import type React from 'react';
import { usePathname } from 'next/navigation';
import {
  Building2,
  Users,
  Home,
  Search,
  Bell,
  DollarSign,
  Calculator,
  CreditCard,
  Menu,
  Shield,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { AuthGuard } from '@/components/auth/auth-guard';
import { useState } from 'react';
import { PermissionManager, EMPLOYER_NAV_ITEMS } from '@/lib/permissions';
import Link from 'next/link';
import { useAuthStore } from '@/store/auth-store';
import Image from 'next/image';
import { EngagespotNotification } from '@/lib/engagespot-notification';
import { useLogout } from '@/hooks/useLogout';

// Icon mapping for navigation items
const iconMap = {
  Home,
  Users,
  DollarSign,
  Calculator,
  CreditCard,
  Shield,
};

function EmployerSidebar({ className = '' }: { className?: string }) {
  const pathname = usePathname();
  const permissionManager = PermissionManager.getInstance();

  // Filter navigation items based on user permissions
  const filteredNavItems = permissionManager.getFilteredNavItems(EMPLOYER_NAV_ITEMS);

  const isActiveRoute = (itemUrl: string) => {
    return pathname === itemUrl;
  };

  return (
    <div className={`border-r border-gray-200 bg-white ${className}`}>
      <div className="flex h-[68px] items-center justify-center border-b border-gray-200">
        <div className="flex items-center justify-center rounded-lg text-white">
          <Image
            src="/logo_finwage.svg"
            alt="FinWage Logo"
            width={200}
            height={100}
            className=""
            priority
          />
        </div>
      </div>

      <nav className="p-4">
        <div className="space-y-1">
          {filteredNavItems.map((item) => {
            const IconComponent = iconMap[item.icon as keyof typeof iconMap];
            const isActive = isActiveRoute(item.url);

            return (
              <Link key={item.title} href={item.url}>
                <button
                  data-active={isActive}
                  className={`hover:bg-primary/10 data-[active=true]:border-primary data-[active=true]:bg-primary/10 data-[active=true]:text-primary relative my-2 flex w-full cursor-pointer items-center gap-3 rounded-lg px-4 py-2 text-left text-sm transition-colors`}
                >
                  <IconComponent
                    className={`h-4 w-4 flex-shrink-0 ${isActive ? 'text-primary' : 'text-primary'}`}
                  />
                  <span className="truncate font-medium">{item.title}</span>
                </button>
              </Link>
            );
          })}
        </div>
      </nav>
    </div>
  );
}

function EmployerUserDropdown() {
  const userRole = localStorage.getItem('userRole');
  const { userDetails } = useAuthStore();
  const { logout } = useLogout();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="flex h-auto items-center gap-2 p-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/placeholder.svg?height=32&width=32" />
            <AvatarFallback className="bg-primary text-white">
              {userRole === 'sub_admin' ? 'SA' : 'EM'}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-start text-sm">
            <span className="line-clamp-1 overflow-hidden font-medium break-words">
              {userDetails?.firstName + ' ' + userDetails?.lastName}
            </span>
            <span className="line-clamp-1 overflow-hidden text-xs break-words text-gray-500">
              {userDetails?.email}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <Link href="/employer/profile">
          <DropdownMenuItem>Profile</DropdownMenuItem>
        </Link>
        <DropdownMenuItem>Settings</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={logout}>Sign out</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface EmployerLayoutProps {
  children: React.ReactNode;
  title: string;
}

export function EmployerLayout({ children, title }: EmployerLayoutProps) {
  const { userDetails } = useAuthStore();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <AuthGuard allowedRoles={['employer', 'sub_admin']}>
      <div className="flex h-screen bg-gray-50">
        {/* Desktop Sidebar - Increased width */}
        <div className="hidden w-64 flex-shrink-0 lg:block">
          <EmployerSidebar className="h-full" />
        </div>

        {/* Mobile Sidebar */}
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetContent side="left" className="w-72 p-0">
            <EmployerSidebar className="h-full" />
          </SheetContent>
        </Sheet>

        {/* Main Content */}
        <div className="flex min-w-0 flex-1 flex-col">
          {/* Header */}
          <header className="flex h-[68px] items-center border-b border-gray-200 bg-white px-4">
            <div className="flex w-full items-center justify-between">
              <div className="flex items-center gap-4">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="icon" className="lg:hidden">
                      <Menu className="h-5 w-5" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-72 p-0">
                    <EmployerSidebar className="h-full" />
                  </SheetContent>
                </Sheet>
                <div></div>
              </div>

              <div className="flex items-center gap-4">
                {userDetails?.id && <EngagespotNotification userId={userDetails?.id} />}
                <EmployerUserDropdown />
              </div>
            </div>
          </header>

          {/* Page Content */}
          <main className="flex-1 overflow-auto bg-white p-6">
            <div className="max-w-full">{children}</div>
          </main>
        </div>
      </div>
    </AuthGuard>
  );
}
