'use client';

import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Check, ChevronDown, Search, X } from 'lucide-react';
import { cn } from '@/lib/utils';

// Custom debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export interface PaginatedSelectOption {
  id: string;
  label: string;
  sublabel?: string;
  avatar?: string;
}

export interface PaginatedSelectProps<T = any> {
  value?: string;
  onValueChange: (value: string, option: T) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  required?: boolean;
  className?: string;
  useQuery: (params: { page: number; limit: number; search: string }) => {
    data:
      | {
          data: T[];
          meta: {
            total: number;
            currentPage: number;
            lastPage: number;
            perPage: number;
            prev: number | null;
            next: number | null;
          };
        }
      | undefined;
    isLoading: boolean;
    error: any;
  };
  mapToOption: (item: T) => PaginatedSelectOption;
  searchPlaceholder?: string;
  itemsPerPage?: number;
  disabled?: boolean;
}

export function PaginatedSelect<T = any>({
  value,
  onValueChange,
  placeholder = 'Select an option',
  label,
  error,
  required,
  className,
  useQuery,
  mapToOption,
  searchPlaceholder = 'Search...',
  itemsPerPage = 10,
  disabled = false,
}: PaginatedSelectProps<T>) {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 500);
  const [page, setPage] = useState(1);
  const [allOptions, setAllOptions] = useState<T[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Use the provided query hook
  const {
    data,
    isLoading,
    error: queryError,
  } = useQuery({
    page,
    limit: itemsPerPage,
    search: debouncedSearch,
  });

  // Reset page when search changes
  useEffect(() => {
    setPage(1);
    setAllOptions([]);
  }, [debouncedSearch]);

  // Accumulate options when new data comes in
  useEffect(() => {
    if (data?.data) {
      if (page === 1) {
        // For page 1, always replace the data
        setAllOptions(data.data);
      } else {
        // For subsequent pages, only add if the data is not already present
        setAllOptions((prev) => {
          const existingIds = new Set(prev.map((item) => mapToOption(item).id));
          const newItems = data.data.filter((item) => !existingIds.has(mapToOption(item).id));
          return [...prev, ...newItems];
        });
      }
    }
  }, [data, page, mapToOption]);

  // Replace the intersection observer section with:
  const { ref: intersectionRef, inView } = useInView({
    threshold: 0.1,
    rootMargin: '20px',
  });

  // Add useEffect to handle inView changes
  useEffect(() => {
    if (inView && data?.meta && page < data.meta.lastPage && !isLoading) {
      setPage((prev) => prev + 1);
    }
  }, [inView, data?.meta, page, isLoading]);

  const selectedOption = allOptions.find((option) => mapToOption(option).id === value);

  const handleSelect = (option: T) => {
    const mappedOption = mapToOption(option);
    onValueChange(mappedOption.id, option);
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  };

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange('', {} as T);
  };

  return (
    <div className="space-y-2">
      {label && (
        <Label className={cn('!gap-1 text-sm font-medium', error && 'text-red-500')}>
          {label}
          {required && <span className="">*</span>}
        </Label>
      )}

      <Popover open={open} onOpenChange={handleOpenChange} modal>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              'h-11 w-full justify-between',
              !selectedOption && 'text-muted-foreground',
              error && 'border-red-500',
              className
            )}
            disabled={disabled}
          >
            {selectedOption ? (
              <div className="flex min-w-0 flex-1 items-center gap-2">
                {/* {mapToOption(selectedOption).avatar && (
                  <Avatar className="h-5 w-5">
                    <AvatarImage src={mapToOption(selectedOption).avatar || '/placeholder.svg'} />
                    <AvatarFallback className="text-xs">
                      {mapToOption(selectedOption)
                        .label.split(' ')
                        .map((n) => n[0])
                        .join('')
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                )} */}
                <div className="flex min-w-0 flex-col items-start">
                  <span className="truncate font-medium">{mapToOption(selectedOption).label}</span>
                  {mapToOption(selectedOption).sublabel && (
                    <span className="text-muted-foreground truncate text-xs">
                      {mapToOption(selectedOption).sublabel}
                    </span>
                  )}
                </div>
              </div>
            ) : (
              placeholder
            )}
            <div className="flex items-center gap-1">
              {/* {selectedOption && (
                <X className="h-4 w-4 opacity-50 hover:opacity-100" onClick={clearSelection} />
              )} */}
              <ChevronDown className="h-4 w-4 opacity-50" />
            </div>
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-(--radix-popover-trigger-width) p-0">
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              ref={searchInputRef}
              placeholder={searchPlaceholder}
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          <ScrollArea className="h-[300px]">
            <div className="p-1">
              {isLoading && page === 1 ? (
                // Initial loading state
                <div className="space-y-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-2 p-2">
                      <Skeleton className="h-6 w-6 rounded-full" />
                      <div className="flex-1 space-y-1">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : queryError ? (
                <div className="text-muted-foreground p-4 text-center text-sm">
                  Error loading options. Please try again.
                </div>
              ) : allOptions.length === 0 ? (
                <div className="text-muted-foreground p-4 text-center text-sm">
                  No options found.
                </div>
              ) : (
                <>
                  {allOptions.map((option) => {
                    const mappedOption = mapToOption(option);
                    const isSelected = mappedOption.id === value;

                    return (
                      <div
                        key={mappedOption.id}
                        className={cn(
                          'hover:bg-accent hover:text-accent-foreground flex cursor-pointer items-center gap-2 rounded-sm p-2',
                          isSelected && 'bg-accent text-accent-foreground'
                        )}
                        onClick={() => handleSelect(option)}
                      >
                        {/* {mappedOption.avatar && (
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={mappedOption.avatar || '/placeholder.svg'} />
                            <AvatarFallback className="bg-primary/10 text-primary text-xs">
                              {mappedOption.label
                                .split(' ')
                                .map((n) => n[0])
                                .join('')
                                .toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        )} */}
                        <div className="flex min-w-0 flex-1 flex-col items-start">
                          <span className="truncate font-medium">{mappedOption.label}</span>
                          {mappedOption.sublabel && (
                            <span className="text-muted-foreground truncate text-xs">
                              {mappedOption.sublabel}
                            </span>
                          )}
                        </div>
                        {isSelected && <Check className="text-primary h-4 w-4" />}
                      </div>
                    );
                  })}

                  {/* Load more trigger */}
                  {data?.meta && page < data.meta.lastPage && (
                    <div ref={intersectionRef} className="p-2">
                      {isLoading ? (
                        <div className="flex items-center justify-center">
                          <div className="flex items-center space-x-2">
                            <div className="border-primary h-4 w-4 animate-spin rounded-full border-b-2"></div>
                            <span className="text-muted-foreground text-sm">Loading more...</span>
                          </div>
                        </div>
                      ) : (
                        <div className="text-muted-foreground text-center text-sm">
                          Scroll for more options
                        </div>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
          </ScrollArea>
        </PopoverContent>
      </Popover>

      {error && <p className="text-sm text-red-600">{error}</p>}
    </div>
  );
}
