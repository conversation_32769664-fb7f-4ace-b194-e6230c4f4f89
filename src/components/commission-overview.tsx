'use client';

import { useState } from 'react';
import {
  Search,
  ChevronLeft,
  ChevronRight,
  DollarSign,
  TrendingUp,
  Building2,
  Zap,
  CalendarIcon,
  Download,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import Header from './reusable-component/header';

// Sample commission data
const commissionData = [
  {
    id: 1,
    transactionId: 'TXN-001',
    employeeName: 'Alice Johnson',
    companyName: 'Tech Solutions Inc.',
    requestAmount: 1000,
    finwayCommission: 3.0,
    companyCommission: 0.5,
    totalCommission: 3.5,
    date: '2024-01-15',
    status: 'completed',
  },
  {
    id: 2,
    transactionId: 'TXN-002',
    employeeName: 'Michael Chen',
    companyName: 'Global Consulting Group',
    requestAmount: 1500,
    finwayCommission: 4.5,
    companyCommission: 0.75,
    totalCommission: 5.25,
    date: '2024-01-14',
    status: 'completed',
  },
  {
    id: 3,
    transactionId: 'TXN-003',
    employeeName: 'Sarah Williams',
    companyName: 'Digital Marketing Pro',
    requestAmount: 800,
    finwayCommission: 2.4,
    companyCommission: 0.4,
    totalCommission: 2.8,
    date: '2024-01-13',
    status: 'completed',
  },
  {
    id: 4,
    transactionId: 'TXN-004',
    employeeName: 'David Rodriguez',
    companyName: 'Startup Innovations',
    requestAmount: 1200,
    finwayCommission: 3.6,
    companyCommission: 0.6,
    totalCommission: 4.2,
    date: '2024-01-12',
    status: 'completed',
  },
  {
    id: 5,
    transactionId: 'TXN-005',
    employeeName: 'Jennifer Lopez',
    companyName: 'Green Energy Co',
    requestAmount: 2000,
    finwayCommission: 6.0,
    companyCommission: 1.0,
    totalCommission: 7.0,
    date: '2024-01-11',
    status: 'completed',
  },
  {
    id: 6,
    transactionId: 'TXN-006',
    employeeName: 'Robert Kim',
    companyName: 'Financial Services Corp',
    requestAmount: 1800,
    finwayCommission: 5.4,
    companyCommission: 0.9,
    totalCommission: 6.3,
    date: '2024-01-10',
    status: 'completed',
  },
  {
    id: 7,
    transactionId: 'TXN-007',
    employeeName: 'Maria Garcia',
    companyName: 'Healthcare Solutions',
    requestAmount: 900,
    finwayCommission: 2.7,
    companyCommission: 0.45,
    totalCommission: 3.15,
    date: '2024-01-09',
    status: 'completed',
  },
  {
    id: 8,
    transactionId: 'TXN-008',
    employeeName: 'James Wilson',
    companyName: 'E-commerce Giants',
    requestAmount: 1600,
    finwayCommission: 4.8,
    companyCommission: 0.8,
    totalCommission: 5.6,
    date: '2024-01-08',
    status: 'completed',
  },
  {
    id: 9,
    transactionId: 'TXN-009',
    employeeName: 'Emma Brown',
    companyName: 'Tech Solutions Inc.',
    requestAmount: 1100,
    finwayCommission: 3.3,
    companyCommission: 0.55,
    totalCommission: 3.85,
    date: '2024-01-07',
    status: 'completed',
  },
  {
    id: 10,
    transactionId: 'TXN-010',
    employeeName: 'Frank Miller',
    companyName: 'Manufacturing Plus',
    requestAmount: 2200,
    finwayCommission: 6.6,
    companyCommission: 1.1,
    totalCommission: 7.7,
    date: '2024-01-06',
    status: 'completed',
  },
  {
    id: 11,
    transactionId: 'TXN-011',
    employeeName: 'Grace Lee',
    companyName: 'Real Estate Pros',
    requestAmount: 1400,
    finwayCommission: 4.2,
    companyCommission: 0.7,
    totalCommission: 4.9,
    date: '2024-01-05',
    status: 'completed',
  },
  {
    id: 12,
    transactionId: 'TXN-012',
    employeeName: 'Henry Taylor',
    companyName: 'Media & Entertainment',
    requestAmount: 1700,
    finwayCommission: 5.1,
    companyCommission: 0.85,
    totalCommission: 5.95,
    date: '2024-01-04',
    status: 'completed',
  },
];

export function CommissionOverview() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [activeTab, setActiveTab] = useState('transactions');
  const [dateFrom, setDateFrom] = useState<Date>(new Date());
  const [dateTo, setDateTo] = useState<Date>(new Date());

  const filteredCommissions = commissionData.filter((commission) => {
    const matchesSearch =
      commission.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      commission.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      commission.transactionId.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  // Pagination calculations
  const totalItems = filteredCommissions.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCommissions = filteredCommissions.slice(startIndex, endIndex);

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number.parseInt(value));
    setCurrentPage(1);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Calculate totals
  const calculateTotals = () => {
    const totalFinwayCommission = commissionData.reduce(
      (sum, item) => sum + item.finwayCommission,
      0
    );
    const totalCompanyCommission = commissionData.reduce(
      (sum, item) => sum + item.companyCommission,
      0
    );
    const totalTransactions = commissionData.length;
    const totalRequestAmount = commissionData.reduce((sum, item) => sum + item.requestAmount, 0);

    return {
      totalFinwayCommission,
      totalCompanyCommission,
      totalCommission: totalFinwayCommission + totalCompanyCommission,
      totalTransactions,
      totalRequestAmount,
    };
  };

  // Calculate company-wise commissions
  const getCompanyWiseCommissions = () => {
    const companyMap = new Map();

    commissionData.forEach((commission) => {
      const company = commission.companyName;
      if (companyMap.has(company)) {
        const existing = companyMap.get(company);
        companyMap.set(company, {
          companyName: company,
          totalTransactions: existing.totalTransactions + 1,
          totalRequestAmount: existing.totalRequestAmount + commission.requestAmount,
          finwayCommission: existing.finwayCommission + commission.finwayCommission,
          companyCommission: existing.companyCommission + commission.companyCommission,
          totalCommission: existing.totalCommission + commission.totalCommission,
        });
      } else {
        companyMap.set(company, {
          companyName: company,
          totalTransactions: 1,
          totalRequestAmount: commission.requestAmount,
          finwayCommission: commission.finwayCommission,
          companyCommission: commission.companyCommission,
          totalCommission: commission.totalCommission,
        });
      }
    });

    return Array.from(companyMap.values()).sort((a, b) => b.totalCommission - a.totalCommission);
  };

  const totals = calculateTotals();
  const companyWiseData = getCompanyWiseCommissions();

  return (
    <div className="space-y-6">
      {/* Header with Date Filter */}
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <Header
          title="Commission Overview"
          description="Track Finway and company commission earnings"
        />

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Date Range Filter */}
          <div className="flex items-center gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-[140px] justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateFrom ? format(dateFrom, 'MMM dd') : 'From'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                {/* @ts-ignore */}
                <Calendar mode="single" selected={dateFrom} onSelect={setDateFrom} />
              </PopoverContent>
            </Popover>

            <span className="text-muted-foreground">to</span>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-[140px] justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateTo ? format(dateTo, 'MMM dd') : 'To'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                {/* @ts-ignore */}
                <Calendar mode="single" selected={dateTo} onSelect={setDateTo} />
              </PopoverContent>
            </Popover>
          </div>

          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Minimal Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border border-gray-200 bg-white shadow-sm">
          <CardContent className="">
            <div className="flex items-center justify-between">
              <div>
                <p className="mb-1 text-sm text-gray-600">Total Commission</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(totals.totalCommission)}
                </p>
                <p className="mt-1 flex items-center text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  +12.5% from last period
                </p>
              </div>
              <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-lg">
                <DollarSign className="text-primary h-6 w-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200 bg-white shadow-sm">
          <CardContent className="">
            <div className="flex items-center justify-between">
              <div>
                <p className="mb-1 text-sm text-gray-600">Finway Commission</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(totals.totalFinwayCommission)}
                </p>
                <p className="mt-1 flex items-center text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  +15.2% from last period
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100">
                <Zap className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200 bg-white shadow-sm">
          <CardContent className="">
            <div className="flex items-center justify-between">
              <div>
                <p className="mb-1 text-sm text-gray-600">Company Commission</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(totals.totalCompanyCommission)}
                </p>
                <p className="mt-1 flex items-center text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  +8.7% from last period
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                <Building2 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-gray-200 bg-white shadow-sm">
          <CardContent className="">
            <div className="flex items-center justify-between">
              <div>
                <p className="mb-1 text-sm text-gray-600">Total Transactions</p>
                <p className="text-2xl font-semibold text-gray-900">{totals.totalTransactions}</p>
                <p className="mt-1 flex items-center text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3 w-3" />
                  +23.1% from last period
                </p>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100">
                <TrendingUp className="h-6 w-6 text-gray-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Minimal Commission Breakdown */}
      <Card className="border border-gray-200 bg-white shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-medium">Commission Distribution</CardTitle>
          <CardDescription>Breakdown between Finway and partner companies</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="rounded-lg border border-gray-200 bg-white p-6 text-center">
              <div className="mb-2 text-3xl font-semibold text-orange-600">
                {formatCurrency(totals.totalFinwayCommission)}
              </div>
              <div className="mb-1 text-sm text-gray-600">Finway Commission</div>
              <div className="text-xs text-gray-500">
                {((totals.totalFinwayCommission / totals.totalCommission) * 100).toFixed(1)}% of
                total
              </div>
            </div>

            <div className="rounded-lg border border-gray-200 bg-white p-6 text-center">
              <div className="mb-2 text-3xl font-semibold text-blue-600">
                {formatCurrency(totals.totalCompanyCommission)}
              </div>
              <div className="mb-1 text-sm text-gray-600">Company Commission</div>
              <div className="text-xs text-gray-500">
                {((totals.totalCompanyCommission / totals.totalCommission) * 100).toFixed(1)}% of
                total
              </div>
            </div>

            <div className="border-primary bg-primary/5 rounded-lg border-2 p-6 text-center">
              <div className="text-primary mb-2 text-3xl font-semibold">
                {formatCurrency(totals.totalCommission)}
              </div>
              <div className="mb-1 text-sm text-gray-600">Total Commission</div>
              <div className="text-xs text-gray-500">All transactions combined</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for Transactions and Company-wise view */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="transactions">All Transactions</TabsTrigger>
          <TabsTrigger value="companies">Company-wise</TabsTrigger>
        </TabsList>

        <TabsContent value="transactions" className="mt-6">
          <Card className="border border-gray-200 bg-white shadow-sm">
            <CardHeader className="border-b border-gray-100">
              <CardTitle>Transaction Details</CardTitle>
              <CardDescription>Detailed breakdown of all commission transactions</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="mb-6 flex items-center justify-between">
                <div className="relative max-w-sm">
                  <Search className="text-muted-foreground absolute top-2.5 left-2.5 h-4 w-4" />
                  <Input
                    placeholder="Search transactions..."
                    value={searchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Items per page" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 per page</SelectItem>
                    <SelectItem value="10">10 per page</SelectItem>
                    <SelectItem value="20">20 per page</SelectItem>
                    <SelectItem value="50">50 per page</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Transaction Table */}
              <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50/50">
                      <TableHead className="font-semibold text-gray-900">Transaction</TableHead>
                      <TableHead className="font-semibold text-gray-900">Employee</TableHead>
                      <TableHead className="font-semibold text-gray-900">Company</TableHead>
                      <TableHead className="font-semibold text-gray-900">Request Amount</TableHead>
                      <TableHead className="font-semibold text-gray-900">
                        Finway Commission
                      </TableHead>
                      <TableHead className="font-semibold text-gray-900">
                        Company Commission
                      </TableHead>
                      <TableHead className="font-semibold text-gray-900">Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentCommissions.map((commission) => (
                      <TableRow
                        key={commission.id}
                        className="border-b border-gray-100 hover:bg-gray-50/50"
                      >
                        <TableCell className="py-4">
                          <div className="text-primary font-medium">{commission.transactionId}</div>
                          <Badge className="mt-1 border-green-200 bg-green-50 text-xs text-green-700 hover:bg-green-50">
                            {commission.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={`/placeholder.svg?height=32&width=32`} />
                              <AvatarFallback className="bg-primary/10 text-primary text-xs font-medium">
                                {commission.employeeName
                                  .split(' ')
                                  .map((n) => n[0])
                                  .join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="text-sm font-medium">{commission.employeeName}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="text-sm font-medium">{commission.companyName}</div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="font-semibold text-gray-900">
                            {formatCurrency(commission.requestAmount)}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="font-semibold text-orange-600">
                            {formatCurrency(commission.finwayCommission)}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="font-semibold text-blue-600">
                            {formatCurrency(commission.companyCommission)}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="text-sm text-gray-600">{commission.date}</div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {/* <div className="flex items-center justify-between px-2 py-4">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium">
                    Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems}{' '}
                    entries
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: totalPages }, (_, i) => i + 1)
                      .filter((page) => {
                        return (
                          page === 1 ||
                          page === totalPages ||
                          (page >= currentPage - 1 && page <= currentPage + 1)
                        );
                      })
                      .map((page, index, array) => (
                        <div key={page} className="flex items-center">
                          {index > 0 && array[index - 1] !== page - 1 && (
                            <span className="text-muted-foreground px-2 text-sm">...</span>
                          )}
                          <Button
                            variant={currentPage === page ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                            className={currentPage === page ? 'bg-primary hover:bg-primary/90' : ''}
                          >
                            {page}
                          </Button>
                        </div>
                      ))}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div> */}

              {filteredCommissions.length === 0 && (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">
                    No commission records found matching your search.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="companies" className="mt-6">
          <Card className="border border-gray-200 bg-white shadow-sm">
            <CardHeader className="border-b border-gray-100">
              <CardTitle>Company-wise Commission Breakdown</CardTitle>
              <CardDescription>Commission earnings grouped by partner companies</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50/50">
                      <TableHead className="font-semibold text-gray-900">Company Name</TableHead>
                      <TableHead className="font-semibold text-gray-900">
                        Total Transactions
                      </TableHead>
                      <TableHead className="font-semibold text-gray-900">
                        Total Request Amount
                      </TableHead>
                      <TableHead className="font-semibold text-gray-900">
                        Finway Commission
                      </TableHead>
                      <TableHead className="font-semibold text-gray-900">
                        Company Commission
                      </TableHead>
                      <TableHead className="font-semibold text-gray-900">
                        Total Commission
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {companyWiseData.map((company, index) => (
                      <TableRow
                        key={index}
                        className="border-b border-gray-100 hover:bg-gray-50/50"
                      >
                        <TableCell className="py-4">
                          <div className="flex items-center gap-3">
                            <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
                              <Building2 className="text-primary h-5 w-5" />
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900">
                                {company.companyName}
                              </div>
                              <div className="text-sm text-gray-500">Partner Company</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="text-center">
                            <div className="text-lg font-bold text-gray-900">
                              {company.totalTransactions}
                            </div>
                            <div className="text-xs text-gray-500">transactions</div>
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="font-semibold text-gray-900">
                            {formatCurrency(company.totalRequestAmount)}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="font-semibold text-orange-600">
                            {formatCurrency(company.finwayCommission)}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="font-semibold text-blue-600">
                            {formatCurrency(company.companyCommission)}
                          </div>
                        </TableCell>
                        <TableCell className="py-4">
                          <div className="text-primary text-lg font-bold">
                            {formatCurrency(company.totalCommission)}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
