'use client';

import { useState } from 'react';
import {
  CheckCircle,
  XCircle,
  Clock,
  Building2,
  Search,
  Eye,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import Header from './reusable-component/header';

// Sample approval data
const approvalData = [
  {
    id: 1,
    type: 'employer',
    companyName: 'TechStart Solutions',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Innovation Drive, Tech City, TC 12345',
    submittedDate: '2024-01-15',
    status: 'pending',
    documents: ['Business License', 'Tax Certificate', 'Registration Form'],
  },
  {
    id: 2,
    type: 'employer',
    companyName: 'Digital Marketing Pro',
    contactPerson: 'Michael Chen',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Marketing Boulevard, Business Park, BP 67890',
    submittedDate: '2024-01-12',
    status: 'pending',
    documents: ['Business License', 'Marketing Certification', 'Registration Form'],
  },
  {
    id: 3,
    type: 'employer',
    companyName: 'Green Energy Corp',
    contactPerson: 'David Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Renewable Street, Eco City, EC 54321',
    submittedDate: '2024-01-10',
    status: 'approved',
    documents: ['Business License', 'Environmental Certificate', 'Registration Form'],
  },
  {
    id: 4,
    type: 'employer',
    companyName: 'FinTech Innovations',
    contactPerson: 'Lisa Wang',
    email: '<EMAIL>',
    phone: '+****************',
    address: '321 Finance Avenue, Money District, MD 98765',
    submittedDate: '2024-01-08',
    status: 'rejected',
    documents: ['Business License', 'Financial License', 'Registration Form'],
    rejectionReason: 'Incomplete documentation - missing compliance certificates',
  },
  {
    id: 5,
    type: 'employer',
    companyName: 'Healthcare Plus',
    contactPerson: 'Dr. Amanda Foster',
    email: '<EMAIL>',
    phone: '+****************',
    address: '654 Medical Center Drive, Health City, HC 13579',
    submittedDate: '2024-01-05',
    status: 'pending',
    documents: ['Business License', 'Medical License', 'Registration Form'],
  },
];

export function ApprovalList() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [selectedApproval, setSelectedApproval] = useState<any>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve');

  // Filter approvals
  const filteredApprovals = approvalData.filter((approval) => {
    const matchesSearch =
      approval.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      approval.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
      approval.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || approval.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Pagination calculations
  const totalItems = filteredApprovals.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentApprovals = filteredApprovals.slice(startIndex, endIndex);

  // Reset pagination when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Rejected</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleViewDetails = (approval: any) => {
    setSelectedApproval(approval);
    setDetailDialogOpen(true);
  };

  const handleAction = (approval: any, action: 'approve' | 'reject') => {
    setSelectedApproval(approval);
    setActionType(action);
    setActionDialogOpen(true);
  };

  const confirmAction = () => {
    if (selectedApproval) {
      const actionText = actionType === 'approve' ? 'approved' : 'rejected';
      toast.success(`${selectedApproval.companyName} has been ${actionText} successfully.`);
    }
    setActionDialogOpen(false);
    setSelectedApproval(null);
  };

  const confirmApproval = confirmAction;

  // Calculate statistics
  const pendingCount = approvalData.filter((item) => item.status === 'pending').length;
  const approvedCount = approvalData.filter((item) => item.status === 'approved').length;
  const rejectedCount = approvalData.filter((item) => item.status === 'rejected').length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Header
          title="Approval Management"
          description="Review and manage employer registration applications"
        />
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{pendingCount}</div>
            <p className="text-muted-foreground text-xs">Awaiting review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{approvedCount}</div>
            <p className="text-muted-foreground text-xs">Successfully approved</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{rejectedCount}</div>
            <p className="text-muted-foreground text-xs">Applications rejected</p>
          </CardContent>
        </Card>
      </div>

      {/* Approval List */}
      <Card>
        <CardContent>
          {/* Filters and Search */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="relative max-w-md flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search by company, contact, or email..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  handleFilterChange();
                }}
                className="h-10 pl-10"
              />
            </div>
            <div className="flex gap-3">
              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value);
                  handleFilterChange();
                }}
              >
                <SelectTrigger className="h-10 w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number.parseInt(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="h-10 w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                  <SelectItem value="50">50 rows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Approval Table */}
          <div className="overflow-hidden rounded-lg border border-gray-200">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-gray-900">Company</TableHead>
                  <TableHead className="font-semibold text-gray-900">Contact Person</TableHead>
                  <TableHead className="font-semibold text-gray-900">Submitted Date</TableHead>
                  <TableHead className="font-semibold text-gray-900">Status</TableHead>
                  <TableHead className="text-right font-semibold text-gray-900">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentApprovals.map((approval) => (
                  <TableRow key={approval.id} className="hover:bg-gray-50">
                    <TableCell className="py-4">
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#0A8080]/10">
                          <Building2 className="h-5 w-5 text-[#0A8080]" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{approval.companyName}</div>
                          <div className="text-sm text-gray-600">{approval.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <div>
                        <div className="font-medium text-gray-900">{approval.contactPerson}</div>
                        <div className="text-sm text-gray-600">{approval.phone}</div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <span className="text-gray-900">{approval.submittedDate}</span>
                    </TableCell>
                    <TableCell className="py-4">{getStatusBadge(approval.status)}</TableCell>
                    <TableCell className="py-4 text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetails(approval)}
                          className="h-8"
                        >
                          <Eye className="mr-1 h-4 w-4" />
                          View
                        </Button>
                        {approval.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              onClick={() => handleAction(approval, 'approve')}
                              className="h-8 bg-green-600 hover:bg-green-700"
                            >
                              <CheckCircle className="mr-1 h-4 w-4" />
                              Approve
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleAction(approval, 'reject')}
                              className="h-8"
                            >
                              <XCircle className="mr-1 h-4 w-4" />
                              Reject
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between px-2 py-4">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-700">
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} entries
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter((page) => {
                    return (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    );
                  })
                  .map((page, index, array) => (
                    <div key={page} className="flex items-center">
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="text-muted-foreground px-2 text-sm">...</span>
                      )}
                      <Button
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className={currentPage === page ? 'bg-[#0A8080] hover:bg-[#0A8080]/90' : ''}
                      >
                        {page}
                      </Button>
                    </div>
                  ))}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {filteredApprovals.length === 0 && (
            <div className="py-12 text-center">
              <div className="flex flex-col items-center gap-2">
                <CheckCircle className="h-12 w-12 text-gray-400" />
                <p className="text-lg font-medium text-gray-900">No applications found</p>
                <p className="text-sm text-gray-600">
                  Try adjusting your search criteria or check back later for new applications.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detail Dialog */}
      <Dialog open={detailDialogOpen} onOpenChange={setDetailDialogOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-[#0A8080]" />
              Application Details
            </DialogTitle>
            <DialogDescription>
              {selectedApproval && `Review details for ${selectedApproval.companyName}`}
            </DialogDescription>
          </DialogHeader>
          {selectedApproval && (
            <div className="space-y-6">
              {/* Company Information */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">Company Name</label>
                  <p className="text-sm font-medium text-gray-900">
                    {selectedApproval.companyName}
                  </p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">Status</label>
                  <div>{getStatusBadge(selectedApproval.status)}</div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">Contact Person</label>
                  <p className="text-sm text-gray-900">{selectedApproval.contactPerson}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <p className="text-sm text-gray-900">{selectedApproval.email}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">Phone</label>
                  <p className="text-sm text-gray-900">{selectedApproval.phone}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">Submitted Date</label>
                  <p className="text-sm text-gray-900">{selectedApproval.submittedDate}</p>
                </div>
              </div>

              {/* Address */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">Business Address</label>
                <p className="text-sm text-gray-900">{selectedApproval.address}</p>
              </div>

              {/* Documents */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-600">Submitted Documents</label>
                <div className="flex flex-wrap gap-2">
                  {selectedApproval.documents.map((doc: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {doc}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Rejection Reason */}
              {selectedApproval.status === 'rejected' && selectedApproval.rejectionReason && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">Rejection Reason</label>
                  <div className="rounded-lg border border-red-200 bg-red-50 p-3">
                    <p className="text-sm text-red-800">{selectedApproval.rejectionReason}</p>
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end gap-3 border-t pt-4">
                <Button variant="outline" onClick={() => setDetailDialogOpen(false)}>
                  Close
                </Button>
                {selectedApproval.status === 'pending' && (
                  <>
                    <Button
                      onClick={() => {
                        setDetailDialogOpen(false);
                        handleAction(selectedApproval, 'reject');
                      }}
                      variant="destructive"
                    >
                      <XCircle className="mr-2 h-4 w-4" />
                      Reject
                    </Button>
                    <Button
                      onClick={() => {
                        setDetailDialogOpen(false);
                        handleAction(selectedApproval, 'approve');
                      }}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Action Confirmation Dialog */}
      <Dialog open={actionDialogOpen} onOpenChange={setActionDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {actionType === 'approve' ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              {actionType === 'approve' ? 'Approve Application' : 'Reject Application'}
            </DialogTitle>
            <div>
              {selectedApproval && (
                <div>
                  <div className="text-muted-foreground mb-4 text-sm">
                    Are you sure you want to {actionType} the application from{' '}
                    <strong>{selectedApproval.companyName}</strong>?
                  </div>
                  {actionType === 'approve' && (
                    <div className="text-muted-foreground text-sm">
                      <div className="mb-2">This action will:</div>
                      <ul className="mb-4 list-inside list-disc space-y-1">
                        <li>Activate the employer account</li>
                        <li>Grant access to all platform features</li>
                        <li>Send approval notification</li>
                      </ul>
                    </div>
                  )}
                  <div className="text-muted-foreground text-sm">This action cannot be undone.</div>
                </div>
              )}
            </div>
          </DialogHeader>
          <DialogFooter className="sm:justify-end">
            <Button variant="outline" onClick={() => setActionDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={confirmApproval}
              className={
                actionType === 'approve'
                  ? 'bg-green-600 hover:bg-green-700'
                  : 'bg-red-600 hover:bg-red-700'
              }
            >
              {actionType === 'approve' ? (
                <CheckCircle className="mr-2 h-4 w-4" />
              ) : (
                <XCircle className="mr-2 h-4 w-4" />
              )}
              {actionType === 'approve' ? 'Approve' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
