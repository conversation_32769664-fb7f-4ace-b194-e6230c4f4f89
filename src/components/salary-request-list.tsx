'use client';
import { useState } from 'react';
import {
  Search,
  MoreHorizontal,
  Eye,
  Check,
  X,
  Clock,
  TrendingUp,
  Users,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Header from './reusable-component/header';

// Sample salary request data
const salaryRequests = [
  {
    id: 'SR001',
    employeeId: 'EMP001',
    employeeName: '<PERSON>',
    employeeEmail: '<EMAIL>',
    department: 'Engineering',
    position: 'Senior Developer',
    currentSalary: 75000,
    requestedSalary: 85000,
    increaseAmount: 10000,
    increasePercentage: 13.33,
    reason: 'Performance review and market adjustment',
    status: 'pending',
    requestDate: '2024-01-15',
    reviewDate: null,
    reviewedBy: null,
    comments:
      'Requesting salary increase based on excellent performance in Q4 2023 and additional responsibilities taken on.',
  },
  {
    id: 'SR002',
    employeeId: 'EMP002',
    employeeName: 'Sarah Johnson',
    employeeEmail: '<EMAIL>',
    department: 'Marketing',
    position: 'Marketing Manager',
    currentSalary: 65000,
    requestedSalary: 72000,
    increaseAmount: 7000,
    increasePercentage: 10.77,
    reason: 'Promotion and increased responsibilities',
    status: 'approved',
    requestDate: '2024-01-10',
    reviewDate: '2024-01-18',
    reviewedBy: 'HR Manager',
    comments: 'Promotion to Senior Marketing Manager with team leadership responsibilities.',
  },
  {
    id: 'SR003',
    employeeId: 'EMP003',
    employeeName: 'Mike Davis',
    employeeEmail: '<EMAIL>',
    department: 'Sales',
    position: 'Sales Representative',
    currentSalary: 55000,
    requestedSalary: 62000,
    increaseAmount: 7000,
    increasePercentage: 12.73,
    reason: 'Exceeded sales targets consistently',
    status: 'rejected',
    requestDate: '2024-01-08',
    reviewDate: '2024-01-16',
    reviewedBy: 'Sales Director',
    comments: 'Exceeded Q4 targets by 150%. Consistently top performer for 6 months.',
  },
  {
    id: 'SR004',
    employeeId: 'EMP004',
    employeeName: 'Emily Chen',
    employeeEmail: '<EMAIL>',
    department: 'Design',
    position: 'UI/UX Designer',
    currentSalary: 68000,
    requestedSalary: 75000,
    increaseAmount: 7000,
    increasePercentage: 10.29,
    reason: 'Market rate adjustment',
    status: 'under_review',
    requestDate: '2024-01-12',
    reviewDate: null,
    reviewedBy: null,
    comments: 'Research shows current salary below market rate for similar positions in the area.',
  },
  {
    id: 'SR005',
    employeeId: 'EMP005',
    employeeName: 'David Wilson',
    employeeEmail: '<EMAIL>',
    department: 'Engineering',
    position: 'Frontend Developer',
    currentSalary: 70000,
    requestedSalary: 78000,
    increaseAmount: 8000,
    increasePercentage: 11.43,
    reason: 'Skill development and certification completion',
    status: 'pending',
    requestDate: '2024-01-14',
    reviewDate: null,
    reviewedBy: null,
    comments:
      'Completed React certification and AWS certification. Leading frontend architecture decisions.',
  },
  {
    id: 'SR006',
    employeeId: 'EMP006',
    employeeName: 'Lisa Anderson',
    employeeEmail: '<EMAIL>',
    department: 'HR',
    position: 'HR Specialist',
    currentSalary: 58000,
    requestedSalary: 64000,
    increaseAmount: 6000,
    increasePercentage: 10.34,
    reason: 'Annual performance review',
    status: 'approved',
    requestDate: '2024-01-05',
    reviewDate: '2024-01-12',
    reviewedBy: 'HR Director',
    comments: 'Excellent performance in employee relations and recruitment activities.',
  },
];

export function SalaryRequestList() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter requests based on search and filters
  const filteredRequests = salaryRequests.filter((request) => {
    const matchesSearch =
      request.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.id.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    const matchesDepartment = departmentFilter === 'all' || request.department === departmentFilter;

    return matchesSearch && matchesStatus && matchesDepartment;
  });

  // Pagination
  const totalItems = filteredRequests.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentRequests = filteredRequests.slice(startIndex, endIndex);

  // Statistics
  const totalRequests = salaryRequests.length;
  const pendingRequests = salaryRequests.filter((r) => r.status === 'pending').length;
  const approvedRequests = salaryRequests.filter((r) => r.status === 'approved').length;
  const underReviewRequests = salaryRequests.filter((r) => r.status === 'under_review').length;
  const averageIncrease =
    salaryRequests.reduce((sum, r) => sum + r.increasePercentage, 0) / salaryRequests.length;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Pending
          </Badge>
        );
      case 'approved':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
            Approved
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-800 hover:bg-red-100">
            Rejected
          </Badge>
        );
      case 'under_review':
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            Under Review
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleApprove = (requestId: string) => {
    console.log('Approve request:', requestId);
    // Handle approval logic
  };

  const handleReject = (requestId: string) => {
    console.log('Reject request:', requestId);
    // Handle rejection logic
  };

  const handleViewDetails = (requestId: string) => {
    console.log('View details:', requestId);
    // Handle view details logic
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Header
          title="Salary Requests"
          description="Manage and review employee salary increase requests"
        />
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
            <Users className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRequests}</div>
            <p className="text-muted-foreground text-xs">All time requests</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingRequests + underReviewRequests}</div>
            <p className="text-muted-foreground text-xs">Awaiting decision</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <Check className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{approvedRequests}</div>
            <p className="text-muted-foreground text-xs">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Increase</CardTitle>
            <TrendingUp className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageIncrease.toFixed(1)}%</div>
            <p className="text-muted-foreground text-xs">Average requested</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
            <div className="flex flex-1 items-center space-x-4">
              <div className="relative max-w-sm flex-1">
                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search requests..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>

              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="Engineering">Engineering</SelectItem>
                  <SelectItem value="Marketing">Marketing</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Design">Design</SelectItem>
                  <SelectItem value="HR">HR</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number.parseInt(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                  <SelectItem value="50">50 rows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Current Salary</TableHead>
                <TableHead>Requested Salary</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Request Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentRequests.map((request) => (
                <TableRow key={request.id} className="hover:bg-gray-50">
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={`/placeholder.svg?height=32&width=32`} />
                        <AvatarFallback className="bg-primary text-xs text-white">
                          {request.employeeName
                            .split(' ')
                            .map((n) => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium text-gray-900">{request.employeeName}</div>
                        <div className="text-sm text-gray-500">{request.position}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">{formatCurrency(request.currentSalary)}</span>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium text-green-600">
                      {formatCurrency(request.requestedSalary)}
                    </span>
                  </TableCell>
                  <TableCell>{getStatusBadge(request.status)}</TableCell>
                  <TableCell>
                    <span className="text-sm">
                      {new Date(request.requestDate).toLocaleDateString()}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(request.id)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        {(request.status === 'pending' || request.status === 'under_review') && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleApprove(request.id)}
                              className="text-green-600"
                            >
                              <Check className="mr-2 h-4 w-4" />
                              Approve
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleReject(request.id)}
                              className="text-red-600"
                            >
                              <X className="mr-2 h-4 w-4" />
                              Reject
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          <div className="flex items-center justify-between px-2 py-4">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} entries
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter((page) => {
                    return (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    );
                  })
                  .map((page, index, array) => (
                    <div key={page} className="flex items-center">
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="text-muted-foreground px-2 text-sm">...</span>
                      )}
                      <Button
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className={currentPage === page ? 'bg-primary hover:bg-primary/90' : ''}
                      >
                        {page}
                      </Button>
                    </div>
                  ))}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
