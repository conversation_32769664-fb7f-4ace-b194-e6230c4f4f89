'use client';

import { useState } from 'react';
import {
  Plus,
  Search,
  MoreHorizontal,
  Mail,
  UserX,
  Eye,
  ChevronLeft,
  ChevronRight,
  Shield,
  Crown,
  Calendar,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { InviteAdminModal } from './invite-admin-modal';
import { usePermissions } from '@/lib/permissions';
import Header from '../reusable-component/header';

// Sample admin data with owner
const adminData = [
  {
    id: '1',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    role: 'Owner',
    status: 'active',
    invitedBy: 'System',
    invitedDate: '2024-01-01',
    lastActive: '2024-01-08',
  },
  {
    id: '2',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'active',
    invitedBy: 'Robert Johnson',
    invitedDate: '2024-01-02',
    lastActive: '2024-01-07',
  },
  {
    id: '3',
    name: 'Michael Chen',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'active',
    invitedBy: 'Robert Johnson',
    invitedDate: '2024-01-03',
    lastActive: '2024-01-06',
  },
  {
    id: '4',
    name: 'Emily Davis',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'invited',
    invitedBy: 'Robert Johnson',
    invitedDate: '2024-01-05',
    lastActive: null,
  },
  {
    id: '5',
    name: 'James Rodriguez',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'active',
    invitedBy: 'Sarah Wilson',
    invitedDate: '2024-01-04',
    lastActive: '2024-01-08',
  },
  {
    id: '6',
    name: 'Lisa Thompson',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'invited',
    invitedBy: 'Robert Johnson',
    invitedDate: '2024-01-06',
    lastActive: null,
  },
];

export function RoleManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  const { hasPermission } = usePermissions();

  // Filter admins
  const filteredAdmins = adminData.filter((admin) => {
    const matchesSearch =
      admin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || admin.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Pagination calculations
  const totalItems = filteredAdmins.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentAdmins = filteredAdmins.slice(startIndex, endIndex);

  // Reset pagination when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'invited':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Invited</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    if (role === 'Owner') {
      return (
        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          <Crown className="mr-1 h-3 w-3" />
          Owner
        </Badge>
      );
    }
    return (
      <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
        <Shield className="mr-1 h-3 w-3" />
        Admin
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Calculate statistics
  const totalUsers = adminData.length;
  const owners = adminData.filter((admin) => admin.role === 'Owner').length;
  const activeAdmins = adminData.filter(
    (admin) => admin.status === 'active' && admin.role === 'Admin'
  ).length;
  const pendingInvites = adminData.filter((admin) => admin.status === 'invited').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Header title="Role Management" description="Manage admin roles and permissions" />

        {hasPermission('roles.invite') && (
          <Button
            onClick={() => setIsInviteModalOpen(true)}
            className="bg-primary hover:bg-primary/90 h-10 px-6"
          >
            <Plus className="mr-2 h-4 w-4" />
            Invite Admin
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-lg">
                <Shield className="text-primary h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{totalUsers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-100">
                <Crown className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Owners</p>
                <p className="text-2xl font-bold text-gray-900">{owners}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Admins</p>
                <p className="text-2xl font-bold text-gray-900">{activeAdmins}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100">
                <Mail className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Invites</p>
                <p className="text-2xl font-bold text-gray-900">{pendingInvites}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Admin List */}
      <Card>
        <CardContent>
          {/* Filters and Search */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="relative max-w-md flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search by name or email..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  handleFilterChange();
                }}
                className="h-10 pl-10"
              />
            </div>
            <div className="flex gap-3">
              <Select
                value={statusFilter}
                onValueChange={(value) => {
                  setStatusFilter(value);
                  handleFilterChange();
                }}
              >
                <SelectTrigger className="h-10 w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="invited">Invited</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number.parseInt(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="h-10 w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                  <SelectItem value="50">50 rows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Admin Table */}
          <div className="overflow-hidden rounded-lg border border-gray-200">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-gray-900">User</TableHead>
                  <TableHead className="font-semibold text-gray-900">Role</TableHead>
                  <TableHead className="font-semibold text-gray-900">Status</TableHead>
                  <TableHead className="font-semibold text-gray-900">Invited By</TableHead>
                  <TableHead className="font-semibold text-gray-900">Invited Date</TableHead>
                  <TableHead className="font-semibold text-gray-900">Last Active</TableHead>
                  <TableHead className="text-right font-semibold text-gray-900">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentAdmins.map((admin) => (
                  <TableRow key={admin.id} className="hover:bg-gray-50">
                    <TableCell className="py-4">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                          <AvatarFallback className="bg-primary/10 text-primary font-medium">
                            {admin.name
                              .split(' ')
                              .map((n) => n[0])
                              .join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-gray-900">{admin.name}</span>
                            {admin.role === 'Owner' && (
                              <Crown className="h-4 w-4 text-yellow-600" />
                            )}
                          </div>
                          <div className="text-sm text-gray-600">{admin.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">{getRoleBadge(admin.role)}</TableCell>
                    <TableCell className="py-4">{getStatusBadge(admin.status)}</TableCell>
                    <TableCell className="py-4">
                      <span className="text-sm text-gray-900">{admin.invitedBy}</span>
                    </TableCell>
                    <TableCell className="py-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-900">
                          {formatDate(admin.invitedDate)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      {admin.lastActive ? (
                        <span className="text-sm text-gray-900">
                          {formatDate(admin.lastActive)}
                        </span>
                      ) : (
                        <span className="text-sm text-gray-500">Never</span>
                      )}
                    </TableCell>
                    <TableCell className="py-4 text-right">
                      {admin.role === 'Owner' ? (
                        <span className="text-sm text-gray-500">No actions</span>
                      ) : (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {admin.status === 'invited' ? (
                              <>
                                <DropdownMenuItem>
                                  <Mail className="mr-2 h-4 w-4" />
                                  Resend Invite
                                </DropdownMenuItem>
                                {/* {hasPermission('roles.revoke_invite') && (
                                  <DropdownMenuItem className="text-red-600">
                                    <UserX className="mr-2 h-4 w-4" />
                                    Revoke Invite
                                  </DropdownMenuItem>
                                )} */}
                              </>
                            ) : (
                              <>
                                {hasPermission('roles.view_profile') && (
                                  <DropdownMenuItem>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Profile
                                  </DropdownMenuItem>
                                )}
                                {hasPermission('roles.view_profile') &&
                                  hasPermission('roles.deactivate') && <DropdownMenuSeparator />}
                                {hasPermission('roles.deactivate') && (
                                  <DropdownMenuItem className="text-red-600">
                                    <UserX className="mr-2 h-4 w-4" />
                                    Deactivate Admin
                                  </DropdownMenuItem>
                                )}
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between px-2 py-4">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-700">
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} entries
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter((page) => {
                    return (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    );
                  })
                  .map((page, index, array) => (
                    <div key={page} className="flex items-center">
                      {index > 0 && array[index - 1] !== page - 1 && (
                        <span className="text-muted-foreground px-2 text-sm">...</span>
                      )}
                      <Button
                        variant={currentPage === page ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setCurrentPage(page)}
                        className={currentPage === page ? 'bg-primary hover:bg-primary/90' : ''}
                      >
                        {page}
                      </Button>
                    </div>
                  ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {filteredAdmins.length === 0 && (
            <div className="py-12 text-center">
              <div className="flex flex-col items-center gap-2">
                <Shield className="h-12 w-12 text-gray-400" />
                <p className="text-lg font-medium text-gray-900">No admins found</p>
                <p className="text-sm text-gray-600">
                  Try adjusting your search criteria or invite a new admin.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Invite Admin Modal */}
      {hasPermission('roles.invite') && (
        <InviteAdminModal isOpen={isInviteModalOpen} onClose={() => setIsInviteModalOpen(false)} />
      )}
    </div>
  );
}
