// Utility function to format date to YYYY-MM-DD for form input
export const formatDateForInput = (dateString: string): string => {
  // Check if it's ISO format (contains 'T') or DD-MM-YYYY format
  if (dateString.includes('T')) {
    // ISO format - keep existing logic
    return dateString.split('T')[0];
  } else {
    // DD-MM-YYYY format - convert to YYYY-MM-DD
    const [day, month, year] = dateString.split('-');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
};

export const formatDate = (dateString: string) => {
  // For dates in DD-MM-YYYY format (like wage.date: "06-08-2025")
  if (dateString.includes('-') && dateString.split('-').length === 3) {
    const parts = dateString.split('-');
    // Check if it's DD-MM-YYYY format
    if (parts[0].length === 2 && parts[1].length === 2 && parts[2].length === 4) {
      const [day, month, year] = parts;
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    }
  }

  // For UTC dates (like createdAt), just take the date part
  const date = new Date(dateString.split('T')[0]);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Format currency
export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};
