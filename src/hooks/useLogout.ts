import { useAuthStore } from '@/store/auth-store';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { PermissionManager } from '@/lib/permissions';

export const useLogout = () => {
  const { logout } = useAuthStore();
  const queryClient = useQueryClient();

  const handleLogout = () => {
    // First redirect to login page to avoid UI flicker
    window.location.href = '/';
    localStorage.clear();

    // Then clear everything (this happens after redirect starts)
    setTimeout(() => {
      // Clear auth store
      logout();

      // Clear all React Query cache
      queryClient.clear();

      // Reset permission manager
      const permissionManager = PermissionManager.getInstance();
      permissionManager.setUserRole(null as any);
    }, 100);
  };

  return { logout: handleLogout };
};
