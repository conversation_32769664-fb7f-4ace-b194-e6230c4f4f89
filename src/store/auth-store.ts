import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type UserDetails = {
  id: string;
  firstName: string;
  email: string;
  lastName?: string;
  roles?: string[];
};

export type CompanyDetails = {
  id: string;
  name: string;
  address: string;
  phoneNumber: string;
  status: 'PENDING' | 'ACTIVE' | 'INACTIVE';
  createdAt: string;
  updatedAt: string;
};

type AuthState = {
  authToken: string | null;
  refreshToken: string | null;
  userId: string | null;
  userDetails: UserDetails | null;
  companyDetails: CompanyDetails | null;
  rememberMe: boolean;
};

type AuthActions = {
  setToken: (authToken: string, refreshToken: string) => void;
  clearToken: () => void;
  setUserId: (userId: string) => void;
  setUserDetails: (userDetails: UserDetails) => void;
  setCompanyDetails: (companyDetails: CompanyDetails) => void;
  setRememberMe: (rememberMe: boolean) => void;
  reset: () => void;
  logout: () => void;
};

const initialState: AuthState = {
  authToken: null,
  refreshToken: null,
  userId: null,
  userDetails: null,
  companyDetails: null,
  rememberMe: false,
};

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set) => ({
      ...initialState,
      setToken: (authToken, refreshToken) => set({ authToken, refreshToken }),
      clearToken: () => set({ authToken: null, refreshToken: null }),
      setUserId: (userId) => set({ userId }),
      setUserDetails: (userDetails) => set({ userDetails }),
      setCompanyDetails: (companyDetails) => set({ companyDetails }),
      setRememberMe: (rememberMe) => set({ rememberMe }),
      reset: () => {
        set(initialState);
      },
      logout: () => {
        localStorage.clear();
      },
    }),
    {
      name: 'auth-store',
    }
  )
);
