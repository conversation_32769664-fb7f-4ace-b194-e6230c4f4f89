'use client';

import { useState, useRef } from 'react';
import { Upload, Download, FileText, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/lib/toast';
import { useValidateCsv, ValidationResult } from '../api/useValidateCsv';
import { useProcessCsv } from '../api/useProcessCsv';

interface UploadCsvModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const UploadCsvModal = ({ isOpen, onClose, onSuccess }: UploadCsvModalProps) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateCsvMutation = useValidateCsv();
  const processCsvMutation = useProcessCsv();

  const handleDownloadSample = async () => {
    try {
      // Fetch the file to avoid triggering NextTopLoader
      const response = await fetch('/sample-wages.csv');
      const blob = await response.blob();

      // Create blob URL and download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'sample-wages.csv';
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the blob URL
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast.error(error);
    }
  };

  const handleFileSelect = (file: File) => {
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      toast.error('Please select a valid CSV file');
      return;
    }
    setUploadedFile(file);
    setValidationResult(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleValidate = async () => {
    if (!uploadedFile) return;

    try {
      const result = await validateCsvMutation.mutateAsync(uploadedFile);
      setValidationResult(result);
    } catch (error) {
      console.error('Validation error:', error);
      toast.error('Failed to validate CSV file');
    }
  };

  const handleSubmit = async () => {
    if (!validationResult || !uploadedFile) return;

    try {
      const payload = {
        fileName: uploadedFile.name,
        entries: validationResult.data.validEntries,
      };

      await processCsvMutation.mutateAsync(payload);

      toast.success('Wages uploaded successfully!');
      if (onSuccess) {
        onSuccess();
      }
      handleClose();
    } catch (error) {
      console.error('Processing error:', error);
      toast.error('Failed to process CSV file');
    }
  };

  const handleClose = () => {
    setUploadedFile(null);
    setValidationResult(null);
    setIsDragOver(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  const hasInvalidEntries = validationResult && validationResult.data.invalidCount > 0;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Upload CSV</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Download Sample CSV */}
          <div className="flex items-center justify-between rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-blue-600" />
              <div>
                <p className="font-medium text-blue-900">Need a sample CSV?</p>
                <p className="text-sm text-blue-700">
                  Download our template to see the correct format
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadSample}
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
            >
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          </div>

          {/* File Upload Section */}
          <div className="space-y-4">
            <div
              className={`rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                isDragOver
                  ? 'border-primary bg-primary/5'
                  : uploadedFile
                    ? 'border-green-300 bg-green-50'
                    : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileInputChange}
                className="hidden"
              />

              {uploadedFile ? (
                <div className="space-y-2">
                  <CheckCircle className="mx-auto h-12 w-12 text-green-600" />
                  <p className="text-lg font-medium text-green-900">{uploadedFile.name}</p>
                  <p className="text-sm text-green-700">
                    File uploaded successfully. Click validate to check the data.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    className="mt-2"
                  >
                    Choose Different File
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div>
                    <p className="text-lg font-medium text-gray-900">
                      Drag and drop your CSV file here
                    </p>
                    <p className="text-sm text-gray-600">or click to browse files</p>
                  </div>
                  <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
                    Choose File
                  </Button>
                </div>
              )}
            </div>

            {/* Validate Button */}
            {uploadedFile && !validationResult && (
              <div className="flex justify-center">
                <Button
                  onClick={handleValidate}
                  disabled={validateCsvMutation.isPending}
                  className="px-8"
                >
                  {validateCsvMutation.isPending ? 'Validating...' : 'Validate CSV'}
                </Button>
              </div>
            )}
          </div>

          {/* Validation Results */}
          {validationResult && (
            <Card>
              <CardContent className="p-6">
                <h3 className="mb-4 text-lg font-semibold">Validation Results</h3>

                {/* Summary Stats */}
                <div className="mb-6 grid grid-cols-3 gap-4">
                  <div className="rounded-lg bg-blue-50 p-4 text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {validationResult.data.totalRows}
                    </p>
                    <p className="text-sm text-blue-800">Total Rows</p>
                  </div>
                  <div className="rounded-lg bg-green-50 p-4 text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {validationResult.data.validCount}
                    </p>
                    <p className="text-sm text-green-800">Valid Entries</p>
                  </div>
                  <div className="rounded-lg bg-red-50 p-4 text-center">
                    <p className="text-2xl font-bold text-red-600">
                      {validationResult.data.invalidCount}
                    </p>
                    <p className="text-sm text-red-800">Invalid Entries</p>
                  </div>
                </div>

                {/* Invalid Entries */}
                {hasInvalidEntries && (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                      <h4 className="font-semibold text-red-900">Issues Found</h4>
                    </div>

                    <ScrollArea className="h-48 rounded-lg border p-4">
                      <div className="space-y-3">
                        {validationResult.data.invalidEntries.map((entry, index) => (
                          <div key={index} className="border-l-4 border-red-400 py-2 pl-4">
                            <div className="mb-1 flex items-center gap-2">
                              <Badge variant="destructive" className="text-xs">
                                Row {entry.row}
                              </Badge>
                            </div>
                            <ul className="space-y-1">
                              {entry.errors.map((error, errorIndex) => (
                                <li key={errorIndex} className="text-sm text-red-700">
                                  • {error}
                                </li>
                              ))}
                            </ul>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>

                    <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                      <p className="text-sm text-yellow-800">
                        Please fix the errors in your CSV file and upload again.
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setUploadedFile(null);
                          setValidationResult(null);
                          if (fileInputRef.current) {
                            fileInputRef.current.value = '';
                          }
                        }}
                        className="mt-2"
                      >
                        Upload New File
                      </Button>
                    </div>
                  </div>
                )}

                {/* Submit Button (only show when no invalid entries) */}
                {!hasInvalidEntries && validationResult.data.validCount > 0 && (
                  <div className="flex justify-center pt-4">
                    <Button
                      onClick={handleSubmit}
                      disabled={processCsvMutation.isPending}
                      className="bg-green-600 px-8 hover:bg-green-700"
                    >
                      {processCsvMutation.isPending ? 'Processing...' : 'Submit CSV'}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
