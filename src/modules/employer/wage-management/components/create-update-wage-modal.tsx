'use client';

import type React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DollarSign, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Employee as FullEmployee,
  EmployeesFilters,
  useEmployees,
} from '../../employee/api/useEmployees';
import { PaginatedSelect, PaginatedSelectOption } from '@/components/ui/paginated-select';
import { useState, useEffect } from 'react';
import { useCreateWage } from '../api/useCreateWage';
import { useUpdateWage } from '../api/useUpdateWage';
import { toast } from '@/lib/toast';
import { Wage } from '../api/useWages';
import { formatDateForInput } from '@/lib/helper-function';

// Zod schema for form validation
const wageFormSchema = z.object({
  employeeId: z.string().min(1, 'Please select an employee'),
  amount: z
    .string()
    .min(1, 'Amount is required')
    .refine(
      (val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0,
      'Please enter a valid amount greater than 0'
    ),
  date: z.string().min(1, 'Date is required'),
});

type WageFormData = z.infer<typeof wageFormSchema>;

interface CreateUpdateWageModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit';
  wageData?: Wage;
}

export function CreateUpdateWageModal({
  isOpen,
  onClose,
  mode,
  wageData,
}: CreateUpdateWageModalProps) {
  const form = useForm<WageFormData>({
    resolver: zodResolver(wageFormSchema),
    defaultValues: {
      employeeId: '',
      amount: '',
      date: '',
    },
  });

  const {
    watch,
    setValue,
    handleSubmit,
    formState: { errors },
    reset,
  } = form;

  const createWageMutation = useCreateWage();
  const updateWageMutation = useUpdateWage();

  // Watch employeeId to track selected employee
  const watchedEmployeeId = watch('employeeId');

  // Store selected employee separately since it's not part of form data
  const [selectedEmployee, setSelectedEmployee] = useState<FullEmployee | null>(null);

  // Effect to populate form when editing
  useEffect(() => {
    if (mode === 'edit' && wageData) {
      form.reset({
        employeeId: wageData.employeeId,
        amount: wageData.amount.toString(),
        date: formatDateForInput(wageData.date),
      });
      // For edit mode, we don't need to set selectedEmployee as the PaginatedSelect will be disabled
      // and we only need the employeeId for form validation
      setSelectedEmployee(null);
    } else if (mode === 'create') {
      form.reset({
        employeeId: '',
        amount: '',
        date: '',
      });
      setSelectedEmployee(null);
    }
  }, [mode, wageData, form]);

  // Create a wrapper function that matches the expected signature
  const useEmployeesWrapper = (params: { page: number; limit: number; search: string }) => {
    const filters: EmployeesFilters = {
      page: params.page,
      limit: params.limit,
      search: params.search,
      status: 'ACTIVE', // Only show active employees for wage assignment
    };

    return useEmployees(filters);
  };

  const onSubmit = async (data: WageFormData) => {
    try {
      // Convert amount to number for API submission
      const submissionData = {
        employeeId: data.employeeId,
        amount: parseFloat(data.amount),
        date: data.date,
        workDuration: 1.0,
      };

      if (mode === 'create') {
        await createWageMutation.mutateAsync(submissionData);
        toast.success('Wage added successfully');
      } else {
        await updateWageMutation.mutateAsync({ id: wageData!.id, ...submissionData });
        toast.success('Wage updated successfully');
      }

      // Reset form and close modal
      reset();
      setSelectedEmployee(null);
      onClose();
    } catch (error) {
      toast.error(error);
    }
  };

  const handleClose = () => {
    reset();
    setSelectedEmployee(null);
    onClose();
  };

  // Map employee data to select option format
  const mapEmployeeToOption = (employee: FullEmployee): PaginatedSelectOption => ({
    id: employee.id,
    label: employee.fullName,
    sublabel: employee.email,
    avatar: `/placeholder.svg?height=32&width=32&text=${employee.fullName
      .split(' ')
      .map((n: string) => n[0])
      .join('')}`,
  });

  const handleEmployeeSelect = (employeeId: string, employee: FullEmployee) => {
    setValue('employeeId', employeeId, { shouldValidate: true });
    setSelectedEmployee(employee);
  };

  // Get today's date in YYYY-MM-DD format for max date
  const today = new Date().toISOString().split('T')[0];

  const isLoading = createWageMutation.isPending || updateWageMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {mode === 'create' ? 'Add New Wage' : 'Edit Wage'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Add a new wage entry for an employee.'
              : 'Update the wage entry information below.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Employee Selection */}
            {mode === 'create' ? (
              <FormField
                control={form.control}
                name="employeeId"
                render={({ field }) => (
                  <FormItem>
                    <PaginatedSelect<FullEmployee>
                      value={field.value}
                      onValueChange={handleEmployeeSelect}
                      label="Employee"
                      placeholder="Select an employee"
                      required
                      error={errors.employeeId?.message}
                      useQuery={useEmployeesWrapper}
                      mapToOption={mapEmployeeToOption}
                      searchPlaceholder="Search employees..."
                      itemsPerPage={10}
                    />
                  </FormItem>
                )}
              />
            ) : (
              <div className="space-y-2">
                <FormLabel>Employee</FormLabel>
                <div className="rounded-md border bg-gray-50 p-3">
                  <div className="text-sm font-medium">{wageData?.employee?.fullName}</div>
                  <div className="text-sm text-gray-600">{wageData?.employee?.email}</div>
                </div>
              </div>
            )}

            {/* Amount */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="amount">Amount *</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <DollarSign className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                      <Input
                        {...field}
                        id="amount"
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className={`pl-10 ${errors.amount ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date */}
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="date">Date *</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Calendar className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                      <Input
                        {...field}
                        id="date"
                        type="date"
                        max={today}
                        className={`pl-10 ${errors.date ? 'border-red-500' : ''}`}
                        disabled={isLoading}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 pt-4">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button type="submit" className="bg-primary hover:bg-primary/90" disabled={isLoading}>
                {isLoading
                  ? mode === 'create'
                    ? 'Adding...'
                    : 'Updating...'
                  : mode === 'create'
                    ? 'Add Wage'
                    : 'Update Wage'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

// Keep the old export for backward compatibility during transition
export const AddWageModal = CreateUpdateWageModal;
