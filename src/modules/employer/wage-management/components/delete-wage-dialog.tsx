'use client';

import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useDeleteWage } from '../api/useDeleteWage';
import { Wage } from '../api/useWages';

interface DeleteWageDialogProps {
  isOpen: boolean;
  onClose: () => void;
  wage: Wage | null;
}

export const DeleteWageDialog = ({ isOpen, onClose, wage }: DeleteWageDialogProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: deleteWage } = useDeleteWage();

  const handleDelete = async () => {
    if (!wage) return;

    setIsSubmitting(true);
    try {
      await deleteWage(wage.id);

      toast.success(`Wage entry for ${wage.employee.fullName} has been deleted successfully.`);
      onClose();
    } catch (error) {
      toast.error('Failed to delete wage entry. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const datePart = dateString.split('T')[0]; // Get YYYY-MM-DD part
    const [year, month, day] = datePart.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Delete Wage Entry</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this wage entry? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        {wage && (
          <div className="py-4">
            <div className="space-y-2 rounded-lg border p-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Employee:</span>
                <span className="font-medium">{wage.employee.fullName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Amount:</span>
                <span className="font-medium">${formatCurrency(wage.amount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Date:</span>
                <span className="font-medium">{formatDate(wage.date)}</span>
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isSubmitting}
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Delete Wage
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
