import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

type UpdateWagePayload = {
  employeeId: string;
  amount: number;
  date: string;
};

const updateWageApi = async (id: string, payload: UpdateWagePayload) => {
  const { employeeId, ...rest } = payload;
  const response = await apiClient.patch(`/wages/${id}`, rest);
  return response.data;
};

export const useUpdateWage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, ...payload }: UpdateWagePayload & { id: string }) =>
      updateWageApi(id, payload),
    onSuccess: () => {
      // Invalidate wages list queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['wages-list'] });
    },
  });
};
