import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

type ProcessCsvPayload = {
  fileName: string;
  entries: Array<{
    employeeIdentifier: string;
    amount: number;
    date: string;
    workDuration: number;
  }>;
};

const processCsvApi = async (payload: ProcessCsvPayload) => {
  const response = await apiClient.post('/wages/upload/process', payload);
  return response.data;
};

export const useProcessCsv = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: processCsvApi,
    onSuccess: () => {
      // Invalidate wages list queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['wages-list'] });
    },
  });
};
