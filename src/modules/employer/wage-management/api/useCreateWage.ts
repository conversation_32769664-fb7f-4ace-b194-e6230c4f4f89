import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

type CreateWagePayload = {
  employeeId: string;
  amount: number;
  date: string;
};

const createWageApi = async (payload: CreateWagePayload) => {
  const response = await apiClient.post(`/wages`, payload);
  return response.data;
};

export const useCreateWage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createWageApi,
    onSuccess: () => {
      // Invalidate wages list queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['wages-list'] });
    },
  });
};
