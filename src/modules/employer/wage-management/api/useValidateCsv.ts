import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

export interface ValidationResult {
  statusCode: number;
  message: string;
  data: {
    validEntries: Array<{
      employeeIdentifier: string;
      amount: number;
      date: string;
      workDuration: number;
    }>;
    invalidEntries: Array<{
      row: number;
      errors: string[];
    }>;
    totalRows: number;
    validCount: number;
    invalidCount: number;
  };
  timestamp: string;
  path: string;
  method: string;
}

const validateCsvApi = async (file: File): Promise<ValidationResult> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await apiClient.post('/wages/upload/validate', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response as unknown as ValidationResult;
};

export const useValidateCsv = () => {
  return useMutation({
    mutationFn: validateCsvApi,
  });
};
