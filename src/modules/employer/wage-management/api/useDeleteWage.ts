import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

const deleteWageApi = async (id: string) => {
  const response = await apiClient.delete(`/wages/${id}`);
  return response.data;
};

export const useDeleteWage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteWageApi,
    onSuccess: () => {
      // Invalidate wages list queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['wages-list'] });
    },
  });
};
