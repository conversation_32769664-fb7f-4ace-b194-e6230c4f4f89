import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

interface Employee {
  id: string;
  fullName: string;
  email: string;
  phone: string;
}

export interface Wage {
  id: string;
  employeeId: string;
  amount: number;
  date: string; // ISO string
  workDuration: number;
  entrySource: 'MANUAL' | 'CSV'; // if you expect more sources, use string
  csvUploadId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  createdById: string;
  employee: Employee;
}

export interface Meta {
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

export interface WagesResponse {
  statusCode: number;
  message: string;
  data: Wage[];
  meta: Meta;
  timestamp: string;
  path: string;
  method: string;
}

export interface WagesFilters {
  page?: number;
  perPage?: number;
  employeeId?: string;
  search?: string;
}

const fetchWages = async (filters: WagesFilters = {}): Promise<WagesResponse> => {
  const params = new URLSearchParams();

  // Add pagination parameters
  if (filters.page) {
    params.append('page', filters.page.toString());
  }
  if (filters.perPage) {
    params.append('perPage', filters.perPage.toString());
  }

  // Add filter parameters
  if (filters.employeeId && filters.employeeId.trim()) {
    params.append('employeeId', filters.employeeId.trim());
  }
  if (filters.search && filters.search.trim()) {
    params.append('search', filters.search.trim());
  }

  const queryString = params.toString();
  const url = queryString ? `/wages?${queryString}` : '/wages';

  const response = await apiClient.get(url);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as WagesResponse;
};

export const useWages = (filters: WagesFilters = {}) => {
  return useQuery({
    queryKey: ['wages-list', filters],
    queryFn: () => fetchWages(filters),
  });
};
