'use client';

import { useState } from 'react';
import { Plus, Search, DollarSign, Calendar, User, Edit, Trash2, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { usePermissions } from '@/lib/permissions';
import Header from '@/components/reusable-component/header';
import { CreateUpdateWageModal } from './components/create-update-wage-modal';
import { DeleteWageDialog } from './components/delete-wage-dialog';
import { UploadCsvModal } from './components/upload-csv-modal';
import { DataTable, TableColumn, TableActionItem } from '@/components/table-component/data-table';
import { useWages, Wage, WagesFilters } from './api/useWages';
import { Employee, EmployeesFilters, useEmployees } from '../employee/api/useEmployees';
import { PaginatedSelect, PaginatedSelectOption } from '@/components/ui/paginated-select';
import { formatCurrency } from '@/lib/helper-function';

export const WageManagement = () => {
  const { hasPermission } = usePermissions();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isCreateUpdateWageModalOpen, setIsCreateUpdateWageModalOpen] = useState(false);
  const [isDeleteWageDialogOpen, setIsDeleteWageDialogOpen] = useState(false);
  const [isUploadCsvModalOpen, setIsUploadCsvModalOpen] = useState(false);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string>('');
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [selectedWage, setSelectedWage] = useState<Wage | null>(null);

  // Build filters for wages API
  const wagesFilters: WagesFilters = {
    page: currentPage,
    perPage: itemsPerPage,
    search: searchTerm.trim() || undefined,
    employeeId: selectedEmployeeId || undefined,
  };

  // Fetch wages data
  const { data: wagesResponse, isLoading, error, refetch } = useWages(wagesFilters);

  // Create a wrapper function for employee selection
  const useEmployeesWrapper = (params: { page: number; limit: number; search: string }) => {
    const filters: EmployeesFilters = {
      page: params.page,
      limit: params.limit,
      search: params.search,
      status: 'ACTIVE', // Only show active employees for filtering
    };

    return useEmployees(filters);
  };

  // Handle filter changes
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleEmployeeSelect = (employeeId: string, employee: Employee) => {
    setSelectedEmployeeId(employeeId);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setItemsPerPage(itemsPerPage);
    setCurrentPage(1);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const datePart = dateString.split('T')[0]; // Get YYYY-MM-DD part
    const [year, month, day] = datePart.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Format date for DD-MM-YYYY format
  const formatSimpleDate = (dateString: string) => {
    const [day, month, year] = dateString.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Map employee data to select option format
  const mapEmployeeToOption = (employee: Employee): PaginatedSelectOption => ({
    id: employee.id,
    label: employee.fullName,
    sublabel: employee.email,
    avatar: `/placeholder.svg?height=32&width=32&text=${employee.fullName
      .split(' ')
      .map((n) => n[0])
      .join('')}`,
  });

  // Define table columns
  const columns: TableColumn<Wage>[] = [
    {
      key: 'employee',
      header: 'Employee',
      render: (wage) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
            <AvatarFallback className="bg-primary/10 text-primary font-medium">
              {wage.employee.fullName
                .split(' ')
                .map((n) => n[0])
                .join('')}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium text-gray-900">{wage.employee.fullName}</div>
            <div className="text-sm text-gray-600">{wage.employee.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'date',
      header: 'Date',
      render: (wage) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="font-medium text-gray-900">{formatSimpleDate(wage.date)}</span>
        </div>
      ),
    },
    {
      key: 'amount',
      header: 'Amount',
      render: (wage) => (
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-gray-400" />
          <span className="font-semibold text-gray-900">{formatCurrency(wage.amount)}</span>
        </div>
      ),
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (wage) => <span className="text-sm text-gray-600">{formatDate(wage.createdAt)}</span>,
    },
  ];

  // Define table actions
  const actions: TableActionItem<Wage>[] = [
    ...(hasPermission('wages.edit')
      ? [
          {
            label: 'Edit Wage',
            icon: <Edit className="h-4 w-4" />,
            separator: true,
            onClick: (wage: Wage) => {
              setSelectedWage(wage);
              setModalMode('edit');
              setIsCreateUpdateWageModalOpen(true);
            },
          } as TableActionItem<Wage>,
        ]
      : []),
    ...(hasPermission('wages.delete')
      ? [
          {
            label: 'Delete Wage',
            icon: <Trash2 className="h-4 w-4" />,
            variant: 'destructive',
            className: 'text-red-600',
            onClick: (wage: Wage) => {
              setSelectedWage(wage);
              setIsDeleteWageDialogOpen(true);
            },
          } as TableActionItem<Wage>,
        ]
      : []),
  ];

  // Prepare data for DataTable
  const wages = wagesResponse?.data || [];
  const totalItems = wagesResponse?.meta.total || 0;
  const totalPages = wagesResponse?.meta.totalPages || 1;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Header title="Wage Management" description="Manage employee wages and payments" />

        {hasPermission('wages.create') && (
          <div className="flex gap-3">
            <Button
              onClick={() => setIsUploadCsvModalOpen(true)}
              variant="outline"
              className="h-10 px-6"
            >
              <Upload className="mr-2 h-4 w-4" />
              Upload CSV
            </Button>
            <Button
              onClick={() => {
                setModalMode('create');
                setSelectedWage(null);
                setIsCreateUpdateWageModalOpen(true);
              }}
              className="bg-primary hover:bg-primary/90 h-10 px-6"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Wage
            </Button>
          </div>
        )}
      </div>

      {/* Wages List */}
      <Card>
        <CardContent>
          {/* Filters and Search */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            {/* <div className="relative max-w-md flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search by name"
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="h-11 pl-10"
              />
            </div> */}
            <div className="flex gap-3">
              <div className="w-[280px]">
                <PaginatedSelect<Employee>
                  value={selectedEmployeeId}
                  onValueChange={handleEmployeeSelect}
                  label=""
                  placeholder="Filter by employee"
                  useQuery={useEmployeesWrapper}
                  mapToOption={mapEmployeeToOption}
                  searchPlaceholder="Search employees..."
                  itemsPerPage={10}
                  className="h-11"
                />
              </div>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => handleItemsPerPageChange(Number.parseInt(value))}
              >
                <SelectTrigger className="!h-11 w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                  <SelectItem value="50">50 rows</SelectItem>
                </SelectContent>
              </Select>
              {(selectedEmployeeId || searchTerm) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedEmployeeId('');
                    setSearchTerm('');
                    setCurrentPage(1);
                  }}
                  className="h-11 px-3"
                >
                  Clear All
                </Button>
              )}
            </div>
          </div>

          {/* Data Table */}
          <DataTable
            columns={columns}
            data={wages}
            meta={{
              total: totalItems,
              lastPage: totalPages,
              currentPage: currentPage,
            }}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            actions={actions}
            isLoading={isLoading}
            error={error ? 'Error loading wages. Please try again.' : null}
            emptyState={{
              icon: <DollarSign className="h-12 w-12 text-gray-400" />,
              title: 'No wages found',
              description: 'Try adjusting your search criteria or add a new wage entry.',
            }}
            getRowKey={(wage) => wage.id}
          />
        </CardContent>
      </Card>

      {/* Create/Update Wage Modal */}
      {(hasPermission('wages.create') || hasPermission('wages.edit')) && (
        <CreateUpdateWageModal
          isOpen={isCreateUpdateWageModalOpen}
          onClose={() => {
            setIsCreateUpdateWageModalOpen(false);
            setSelectedWage(null);
          }}
          mode={modalMode}
          wageData={selectedWage || undefined}
        />
      )}

      {/* Delete Wage Dialog */}
      {hasPermission('wages.delete') && (
        <DeleteWageDialog
          isOpen={isDeleteWageDialogOpen}
          onClose={() => {
            setIsDeleteWageDialogOpen(false);
            setSelectedWage(null);
          }}
          wage={selectedWage}
        />
      )}

      {/* Upload CSV Modal */}
      {hasPermission('wages.create') && (
        <UploadCsvModal
          isOpen={isUploadCsvModalOpen}
          onClose={() => setIsUploadCsvModalOpen(false)}
          onSuccess={() => refetch()}
        />
      )}
    </div>
  );
};
