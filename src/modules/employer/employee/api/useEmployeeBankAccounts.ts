import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export type AccountType = 'SAVINGS' | 'CURRENT' | 'SALARY';
export type BankAccountStatus = 'PENDING' | 'VERIFIED' | 'REJECTED' | 'ACTIVE';
export type VerificationStatus = 'PENDING' | 'VERIFIED' | 'FAILED';

export interface BankAccountVerification {
  provider: string;
  reference: string;
  status: VerificationStatus;
  lastVerificationAt: string;
  data: {
    bankName: string;
    ifscCode: string;
    verified: boolean;
    timestamp: string;
    accountName: string;
  };
}

export interface BankAccount {
  id: string;
  employeeId: string;
  accountHolderName: string;
  accountNumber: string;
  routingNumber: string;
  bankName: string;
  branchName: string;
  accountType: AccountType;
  status: BankAccountStatus;
  isPrimary: boolean;
  rejectionReason: string | null;
  verification: BankAccountVerification;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface BankAccountsResponse {
  statusCode: number;
  message: string;
  data: BankAccount[];
  meta: {
    total: number;
    lastPage: number;
    currentPage: number;
    perPage: number;
    prev: number | null;
    next: number | null;
  };
  timestamp: string;
  path: string;
  method: string;
}

export interface BankAccountsFilters {
  page?: number;
  limit?: number;
  status?: BankAccountStatus | 'all';
  accountType?: AccountType | 'all';
  isPrimary?: boolean;
}

const fetchBankAccounts = async (
  employeeId: string,
  filters: BankAccountsFilters = {}
): Promise<BankAccountsResponse> => {
  const params = new URLSearchParams();

  // Add pagination parameters
  if (filters.page) {
    params.append('page', filters.page.toString());
  }
  if (filters.limit) {
    params.append('limit', filters.limit.toString());
  }

  // Add filter parameters
  // if (filters.status && filters.status !== 'all') {
  //   params.append('status', filters.status);
  // }
  // if (filters.accountType && filters.accountType !== 'all') {
  //   params.append('accountType', filters.accountType);
  // }
  // if (filters.isPrimary !== undefined) {
  //   params.append('isPrimary', filters.isPrimary.toString());
  // }

  const queryString = params.toString();
  const url = queryString
    ? `/employees/${employeeId}/bank-accounts?${queryString}`
    : `/employees/${employeeId}/bank-accounts`;

  const response = await apiClient.get(url);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as BankAccountsResponse;
};

export const useEmployeeBankAccounts = (employeeId: string, filters: BankAccountsFilters = {}) => {
  return useQuery({
    queryKey: ['bank-accounts-employee', employeeId, filters],
    queryFn: () => fetchBankAccounts(employeeId, filters),
    enabled: !!employeeId, // Only run query if employeeId is provided
  });
};
