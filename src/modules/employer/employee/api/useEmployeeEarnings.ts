import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface WageEntry {
  id: string;
  employeeId: string;
  amount: number;
  date: string;
  workDuration: number;
  entrySource: 'MANUAL' | 'CSV_UPLOAD';
  csvUploadId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  createdById: string;
  employee: {
    id: string;
    fullName: string;
    email: string;
    phone: string;
  };
}

export interface EmployeeEarningsData {
  totalEarnings: number;
  totalDays: number;
  periodStart: string;
  periodEnd: string;
  wages: WageEntry[];
}

export interface EmployeeEarningsResponse {
  statusCode: number;
  message: string;
  data: EmployeeEarningsData;
  timestamp: string;
  path: string;
  method: string;
}

export interface EmployeeEarningsFilters {
  startDate: string; // Format: YYYY-MM-DD
  endDate: string; // Format: YYYY-MM-DD
  employeeId: string;
}

const fetchEmployeeEarnings = async (
  filters: EmployeeEarningsFilters
): Promise<EmployeeEarningsResponse> => {
  const params = new URLSearchParams();

  // Add required parameters
  params.append('startDate', filters.startDate);
  params.append('endDate', filters.endDate);
  params.append('employeeId', filters.employeeId);

  const url = `/wages/earnings/employee?${params.toString()}`;

  const response = await apiClient.get(url);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as EmployeeEarningsResponse;
};

export const useEmployeeEarnings = (filters: EmployeeEarningsFilters) => {
  return useQuery({
    queryKey: ['employee-earnings', filters],
    queryFn: () => fetchEmployeeEarnings(filters),
    enabled: !!(filters.startDate && filters.endDate && filters.employeeId),
  });
};
