'use client';

import { useState } from 'react';
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building2,
  Edit,
  Shield,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useEmployee } from '../api/useEmployee';
import { useRouter } from 'nextjs-toploader/app';
import { CreateUpdateEmployeeModal } from './create-update-employee-modal';
import Header from '@/components/reusable-component/header';
import { BankAccountCard } from './bank-account-card';
import { BankAccount, useEmployeeBankAccounts } from '../api/useEmployeeBankAccounts';
import { SkeletonBankCard } from './skeleton-bank-card';

interface EmployeeDetailProps {
  employeeId: string;
}

export function EmployeeDetail({ employeeId }: EmployeeDetailProps) {
  const router = useRouter();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Fetch employee data using the API
  const { data: employeeResponse, isLoading, error } = useEmployee(employeeId);
  const {
    data: bankData,
    isLoading: isBankDetailsLoading,
    error: bankError,
  } = useEmployeeBankAccounts(employeeId, {
    page: 1,
    limit: 20,
  });

  const employee = employeeResponse?.data;
  const bankDetails = bankData?.data;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex h-full flex-col items-center justify-center py-20">
        <Loader2 className="text-primary mb-4 h-8 w-8 animate-spin" />
        <h2 className="mb-4 text-2xl font-bold">Loading Employee Details</h2>
        <p className="text-gray-600">Please wait while we fetch the employee information...</p>
      </div>
    );
  }

  // Error state
  if (error || !employee) {
    return (
      <div className="flex h-full flex-col items-center justify-center py-20">
        <h2 className="mb-4 text-2xl font-bold">Employee Not Found</h2>
        <p className="mb-8 text-gray-600">
          The employee you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission
          to view it.
        </p>
        <Button onClick={() => router.back()} className="bg-primary hover:bg-primary/90">
          Back to Employees
        </Button>
      </div>
    );
  }
  const getStatusBadge = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    switch (normalizedStatus) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>;
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getKycStatusBadge = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    const statusConfig = {
      verified: {
        icon: CheckCircle,
        color: 'text-green-600',
        bg: 'bg-green-100',
        text: 'text-green-800',
      },
      pending: {
        icon: Clock,
        color: 'text-yellow-600',
        bg: 'bg-yellow-100',
        text: 'text-yellow-800',
      },
      submitted: {
        icon: Clock,
        color: 'text-blue-600',
        bg: 'bg-blue-100',
        text: 'text-blue-800',
      },
      rejected: {
        icon: XCircle,
        color: 'text-red-600',
        bg: 'bg-red-100',
        text: 'text-red-800',
      },
    };
    const config =
      statusConfig[normalizedStatus as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    return (
      <Badge className={`${config.bg} ${config.text} hover:${config.bg} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {normalizedStatus.charAt(0).toUpperCase() + normalizedStatus.slice(1)}
      </Badge>
    );
  };

  const getBankStatusBadge = (status: string) => {
    const normalizedStatus = status.toLowerCase();
    const statusConfig = {
      verified: {
        icon: CheckCircle,
        color: 'text-green-600',
        bg: 'bg-green-100',
        text: 'text-green-800',
      },
      pending: {
        icon: Clock,
        color: 'text-yellow-600',
        bg: 'bg-yellow-100',
        text: 'text-yellow-800',
      },
      submitted: {
        icon: Clock,
        color: 'text-blue-600',
        bg: 'bg-blue-100',
        text: 'text-blue-800',
      },
      rejected: {
        icon: XCircle,
        color: 'text-red-600',
        bg: 'bg-red-100',
        text: 'text-red-800',
      },
    };
    const config =
      statusConfig[normalizedStatus as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    return (
      <Badge className={`${config.bg} ${config.text} hover:${config.bg} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {normalizedStatus.charAt(0).toUpperCase() + normalizedStatus.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.back()}
            className="h-10 w-10 bg-transparent"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Header
            title={employee.fullName}
            description="Employee details and verification status"
          />
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={() => setIsEditModalOpen(true)}
            className="bg-primary hover:bg-primary/90 h-10 px-6"
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Employee
          </Button>
        </div>
      </div>
      {/* Employee Overview */}
      <div className="">
        {/* Basic Information */}
        <div className="lg:col-span-2">
          <Card className="h-full gap-0">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-3 text-xl">
                <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
                  <User className="text-primary h-5 w-5" />
                </div>
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Status Row */}
              <div className="flex items-center justify-between border-b border-gray-100 py-3">
                <span className="text-sm font-medium text-gray-600">Status</span>
                {getStatusBadge(employee.status)}
              </div>
              {/* Contact Information Grid */}
              <div className="grid gap-6 sm:grid-cols-2">
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Mail className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Email Address</p>
                    <p className="truncate text-sm text-gray-600">{employee.email}</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Phone className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Phone Number</p>
                    <p className="text-sm text-gray-600">{employee.phone}</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <Calendar className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Start Date</p>
                    <p className="text-sm text-gray-600">
                      {new Date(employee.startDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                      })}
                    </p>
                  </div>
                </div>

                {/* Address - Full Width */}
                <div className="flex items-start gap-3 pt-2">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100">
                    <MapPin className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900">Address</p>
                    <p className="text-sm leading-relaxed text-gray-600">{employee.address}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="">
        {/* <Card className="w-fit max-w-full min-w-96"> */}
        <Card className="">
          <CardHeader>
            <CardTitle className="!text-xl font-bold tracking-tight text-gray-900">
              Bank Accounts
            </CardTitle>
            <CardDescription className="text-muted-foreground text-sm">
              {/* Manage your connected bank accounts and view balances */}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4">
            {/* Loading State */}
            {isBankDetailsLoading || !bankDetails ? (
              <div className="flex gap-6 overflow-x-auto pb-4">
                <div className="w-80 flex-shrink-0">
                  <SkeletonBankCard />
                </div>
              </div>
            ) : (
              <>
                {/* All Accounts in Flex Row */}
                {bankDetails.length > 0 && (
                  <div className="flex gap-6 overflow-x-auto pb-4">
                    {bankDetails.map((account) => (
                      <div key={account.id} className="w-80 flex-shrink-0">
                        <BankAccountCard account={account} />
                      </div>
                    ))}
                  </div>
                )}

                {/* Empty State */}
                {bankDetails.length === 0 && (
                  <div className="py-12 text-center">
                    <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-slate-200">
                      <svg
                        className="h-12 w-12 text-slate-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 003 3v8a3 3 0 003 3z"
                        />
                      </svg>
                    </div>
                    <h3 className="mb-2 text-xl font-semibold text-slate-900">
                      No bank accounts found
                    </h3>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Edit Employee Modal */}
      <CreateUpdateEmployeeModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        mode="edit"
        employeeData={employee}
      />
    </div>
  );
}
