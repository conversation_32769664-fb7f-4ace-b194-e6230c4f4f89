import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  CreditCard,
  Shield,
  ShieldCheck,
  ShieldX,
  Star,
  MapPin,
  Hash,
} from 'lucide-react';
import { BankAccount } from '../api/useEmployeeBankAccounts';

interface BankAccountCardProps {
  account: BankAccount;
}

export function BankAccountCard({ account }: BankAccountCardProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'inactive':
        return 'bg-slate-100 text-slate-800 border-slate-200';
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const getVerificationIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'verified':
        return <ShieldCheck className="h-4 w-4 text-emerald-600" />;
      case 'pending':
        return <Shield className="h-4 w-4 text-amber-600" />;
      default:
        return <ShieldX className="h-4 w-4 text-red-600" />;
    }
  };

  const getBankIcon = (bankName: string) => {
    // You can customize this based on actual bank logos
    return <Building2 className="h-6 w-6 text-slate-600" />;
  };

  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="rounded-lg bg-slate-100 p-2">{getBankIcon(account.bankName)}</div>
            <div>
              <h3 className="text-lg leading-tight font-semibold text-slate-900">
                {account.bankName}
              </h3>
              <p className="mt-1 flex items-center gap-1 text-sm text-slate-600">
                <MapPin className="h-3 w-3" />
                {account.branchName}
              </p>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Account Holder */}
        <div>
          <div className="mb-1 flex items-center justify-between">
            <p className="text-xs font-medium tracking-wide text-slate-500 uppercase">
              Account Holder
            </p>
          </div>
          <p className="font-medium text-slate-900 uppercase">{account.accountHolderName}</p>
        </div>

        {/* Account Details Grid - Update to show more relevant info */}
        <div className="grid grid-cols-1 gap-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="mb-1 text-xs font-medium tracking-wide text-slate-500 uppercase">
                Account Number
              </p>
              <p className="flex items-center gap-1 font-mono text-sm text-slate-900">
                <Hash className="h-3 w-3" />
                {account.accountNumber}
              </p>
            </div>
            {account.isPrimary && (
              <Badge variant="secondary" className="px-2 py-1 text-xs">
                Primary
              </Badge>
            )}
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="mb-1 text-xs font-medium tracking-wide text-slate-500 uppercase">
                Account Type
              </p>
              <Badge variant="outline" className="text-xs">
                <CreditCard className="mr-1 h-3 w-3" />
                {account.accountType}
              </Badge>
            </div>
            <div className="text-right">
              <p className="mb-1 text-xs font-medium tracking-wide text-slate-500 uppercase">
                Created
              </p>
              <p className="text-xs text-slate-600">
                {new Date(account.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        {/* Status and Verification */}
        <div className="flex items-center justify-between pt-2">
          <Badge className={getStatusColor(account.status)}>{account.status}</Badge>
          <div className="flex items-center gap-1">
            {getVerificationIcon(account.verification.status)}
            <span className="text-xs text-slate-600">{account.verification.status}</span>
          </div>
        </div>

        {/* Additional Info */}
        <div className="space-y-1 border-t border-slate-100 pt-2">
          <p className="text-xs text-slate-500">
            IFSC: <span className="font-mono font-medium">{account.routingNumber}</span>
          </p>
          <p className="text-xs text-slate-500">
            Reference:{' '}
            <span className="font-mono text-slate-600">{account.verification.reference}</span>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
