'use client';

import { useState, useEffect } from 'react';
import {
  Search,
  Eye,
  Edit,
  Trash2,
  User,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  UserCheck,
  UserX,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Link from 'next/link';
import {
  useEmployees,
  type Employee,
  type EmployeeStatus,
  type KycStatus,
} from '../api/useEmployees';
import { SuspendEmployeeDialog } from './suspend-employee-dialog';
import { ActivateEmployeeDialog } from './activate-employee-dialog';
import {
  DataTable,
  type TableColumn,
  type TableActionItem,
} from '@/components/table-component/data-table';
import { useRouter } from 'nextjs-toploader/app';

interface EmployeeListTableProps {
  onEditEmployee?: (employee: Employee) => void;
}

export const EmployeeListTable = ({ onEditEmployee }: EmployeeListTableProps) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<EmployeeStatus | 'all'>('all');
  const [kycFilter, setKycFilter] = useState<KycStatus | 'all'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Dialog states
  const [suspendDialogOpen, setSuspendDialogOpen] = useState(false);
  const [activateDialogOpen, setActivateDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch employees using the API hook
  const {
    data: employeesData,
    isLoading,
    error,
  } = useEmployees({
    page: currentPage,
    limit: itemsPerPage,
    search: debouncedSearchTerm,
    status: statusFilter,
    kycStatus: kycFilter,
  });

  const employees = employeesData?.data || [];
  const totalItems = employeesData?.meta.total || 0;
  const totalPages = employeesData?.meta.lastPage || 1;

  // Reset pagination when filters change
  const handleFilterChange = () => {
    setCurrentPage(1);
  };

  // Dialog handlers
  const handleSuspendEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setSuspendDialogOpen(true);
  };

  const handleActivateEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setActivateDialogOpen(true);
  };

  const handleCloseSuspendDialog = () => {
    setSuspendDialogOpen(false);
    setSelectedEmployee(null);
  };

  const handleCloseActivateDialog = () => {
    setActivateDialogOpen(false);
    setSelectedEmployee(null);
  };

  const getStatusBadge = (status: EmployeeStatus) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'INACTIVE':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>;
      case 'SUSPENDED':
        return (
          <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">Suspended</Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getKycStatusBadge = (status: KycStatus) => {
    switch (status) {
      case 'VERIFIED':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Verified</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'SUBMITTED':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Submitted</Badge>;
      case 'REJECTED':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getBankStatusBadge = (status: string) => {
    switch (status) {
      case 'VERIFIED':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Verified</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'SUBMITTED':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Submitted</Badge>;
      case 'REJECTED':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  // Define table columns
  const columns: TableColumn<Employee>[] = [
    {
      key: 'employee',
      header: 'Employee',
      render: (employee) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
            <AvatarFallback className="bg-primary/10 text-primary font-medium">
              {employee.fullName
                .split(' ')
                .map((n: any) => n[0])
                .join('')}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium text-gray-900">{employee.fullName}</div>
            <div className="text-sm text-gray-600">{employee.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'contact',
      header: 'Contact',
      render: (employee) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm text-gray-900">
            <Phone className="h-3 w-3" />
            {employee.phone}
          </div>
          <div className="line-clamp-1 flex items-start gap-2 overflow-hidden text-sm break-words text-gray-600">
            <MapPin className="mt-0.5 h-3 w-3 flex-shrink-0" />
            <span className="line-clamp-2">{employee.address}</span>
          </div>
        </div>
      ),
    },
    {
      key: 'startDate',
      header: 'Start Date',
      render: (employee) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-gray-900">{formatDate(employee.startDate)}</span>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (employee) => getStatusBadge(employee.status),
    },
    // {
    //   key: 'kycStatus',
    //   header: 'KYC',
    //   render: (employee) => getKycStatusBadge(employee.kycStatus),
    // },
    // {
    //   key: 'bankStatus',
    //   header: 'Bank',
    //   render: (employee) => getBankStatusBadge(employee.bankStatus),
    // },
  ];

  // Define table actions
  const actions: TableActionItem<Employee>[] = [
    {
      label: 'View Details',
      icon: <Eye className="h-4 w-4" />,
      onClick: (employee) => {
        // Handle view details - can be a Link or router navigation
        router.push(`/employer/employees/${employee.id}`);
      },
    },
    {
      label: 'Manage Wages',
      icon: <DollarSign className="h-4 w-4" />,
      onClick: (employee) => {
        // Navigate to employee wage management page
        router.push(`/employer/employees/${employee.id}/wages`);
      },
    },
    {
      label: 'Edit Employee',
      icon: <Edit className="h-4 w-4" />,
      onClick: (employee) => {
        onEditEmployee?.(employee);
      },
    },
    {
      label: 'Suspend Employee',
      icon: <UserX className="h-4 w-4" />,
      onClick: (employee) => handleSuspendEmployee(employee),
      className: 'text-orange-600',
      show: (employee) => employee.status === 'ACTIVE',
      separator: true,
    },
    {
      label: 'Activate Employee',
      icon: <UserCheck className="h-4 w-4" />,
      onClick: (employee) => handleActivateEmployee(employee),
      className: 'text-green-600',
      show: (employee) => employee.status === 'SUSPENDED',
      separator: true,
    },
    {
      label: 'Remove Employee',
      icon: <Trash2 className="h-4 w-4" />,
      onClick: (employee) => {
        // Handle remove employee
        console.log('Remove employee:', employee.fullName);
      },
      className: 'text-red-600',
    },
  ];

  return (
    <>
      <Card className="gap-0">
        <CardContent>
          {/* Filters and Search */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="relative max-w-md flex-1">
              <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
              <Input
                placeholder="Search by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  handleFilterChange();
                }}
                className="h-10 pl-10"
              />
            </div>

            <div className="flex gap-3">
              <Select
                value={statusFilter}
                onValueChange={(value: string) => {
                  setStatusFilter(value as EmployeeStatus | 'all');
                  handleFilterChange();
                }}
              >
                <SelectTrigger className="!h-10 w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="SUSPENDED">Suspended</SelectItem>
                </SelectContent>
              </Select>

              {/* <Select
                value={kycFilter}
                onValueChange={(value: string) => {
                  setKycFilter(value as KycStatus | 'all');
                  handleFilterChange();
                }}
              >
                <SelectTrigger className="!h-10 w-[140px]">
                  <SelectValue placeholder="KYC Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All KYC</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="SUBMITTED">Submitted</SelectItem>
                  <SelectItem value="VERIFIED">Verified</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                </SelectContent>
              </Select> */}

              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number.parseInt(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="!h-10 w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 rows</SelectItem>
                  <SelectItem value="10">10 rows</SelectItem>
                  <SelectItem value="20">20 rows</SelectItem>
                  <SelectItem value="50">50 rows</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* DataTable - only table and pagination */}
          <DataTable
            columns={columns}
            data={employees}
            meta={{
              total: totalItems,
              lastPage: totalPages,
              currentPage: currentPage,
            }}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={(newItemsPerPage: number) => {
              setItemsPerPage(newItemsPerPage);
              setCurrentPage(1);
            }}
            actions={actions}
            isLoading={isLoading}
            error={error ? 'Error loading employees. Please try again.' : null}
            emptyState={{
              icon: <User className="h-12 w-12 text-gray-400" />,
              title: 'No employees found',
              description: 'Try adjusting your search criteria or add a new employee.',
            }}
            getRowKey={(employee) => employee.id}
          />
        </CardContent>
      </Card>

      {/* Dialogs */}
      <SuspendEmployeeDialog
        isOpen={suspendDialogOpen}
        onClose={handleCloseSuspendDialog}
        employee={selectedEmployee}
      />
      <ActivateEmployeeDialog
        isOpen={activateDialogOpen}
        onClose={handleCloseActivateDialog}
        employee={selectedEmployee}
      />
    </>
  );
};
