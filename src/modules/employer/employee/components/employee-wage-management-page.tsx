'use client';

import { useState } from 'react';
import { useRouter } from 'nextjs-toploader/app';
import { usePermissions } from '@/lib/permissions';
import { useEmployee } from '../api/useEmployee';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { EmployeeWagesManagement } from './wages-management';

interface EmployeeWageManagementPageProps {
  employeeId: string;
}

export function EmployeeWageManagementPage({ employeeId }: EmployeeWageManagementPageProps) {
  const router = useRouter();
  const { hasPermission, canAccessEmployerRoutes } = usePermissions();

  // Check permissions - only employer and sub_admin can access
  const canManageWages = hasPermission('wages_management.view') && canAccessEmployerRoutes();

  // Fetch employee data
  const { data: employee, isLoading, error } = useEmployee(employeeId);

  const handleBack = () => {
    router.push('/employer/employees');
  };

  // Permission check
  if (!canManageWages) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center space-y-4 p-6">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <div className="text-center">
              <h2 className="text-lg font-semibold text-gray-900">Access Denied</h2>
              <p className="text-sm text-gray-600">
                You don&apos;t have permission to manage wages.
              </p>
            </div>
            <Button onClick={handleBack} variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Employees
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent" />
          <p className="mt-2 text-sm text-gray-600">Loading employee details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !employee) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center space-y-4 p-6">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <div className="text-center">
              <h2 className="text-lg font-semibold text-gray-900">Employee Not Found</h2>
              <p className="text-sm text-gray-600">
                The employee you&apos;re looking for doesn&apos;t exist or you don&apos;t have
                access to view them.
              </p>
            </div>
            <Button onClick={handleBack} variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Employees
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto">
      <EmployeeWagesManagement employee={employee.data} onBack={handleBack} />
    </div>
  );
}
