'use client';

import { useState } from 'react';
import { format, addDays } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { ArrowLeft, DollarSign, Calendar } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { DataTable, TableColumn } from '@/components/table-component/data-table';
import Header from '@/components/reusable-component/header';
import { useEmployeeEarnings, WageEntry } from '../api/useEmployeeEarnings';
import { startOfMonth, endOfMonth } from 'date-fns';
import { formatCurrency, formatDate } from '@/lib/helper-function';

interface WagesManagementProps {
  employee: any;
  onBack: () => void;
}

export const EmployeeWagesManagement = ({ employee, onBack }: WagesManagementProps) => {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date()),
  });

  // Format dates for API call
  const startDate = dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : '';
  const endDate = dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : '';

  // Fetch employee earnings using the API
  const {
    data: earningsData,
    isLoading,
    error,
  } = useEmployeeEarnings({
    startDate,
    endDate,
    employeeId: employee.id,
  });

  // Define table columns for wages
  const columns: TableColumn<WageEntry>[] = [
    {
      key: 'date',
      header: 'Date',
      render: (wage) => (
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="font-medium text-gray-900">{formatDate(wage.date)}</span>
        </div>
      ),
    },
    {
      key: 'amount',
      header: 'Amount',
      render: (wage) => (
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-gray-400" />
          <span className="font-semibold text-gray-900">{formatCurrency(wage.amount)}</span>
        </div>
      ),
    },
    // {
    //   key: 'workDuration',
    //   header: 'Work Duration',
    //   render: (wage) => <span className="text-gray-900">{wage.workDuration} hours</span>,
    // },
    {
      key: 'entrySource',
      header: 'Source',
      render: (wage) => (
        <span
          className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
            wage.entrySource === 'MANUAL'
              ? 'bg-blue-100 text-blue-800'
              : 'bg-green-100 text-green-800'
          }`}
        >
          {wage.entrySource === 'MANUAL' ? 'Manual' : 'CSV Upload'}
        </span>
      ),
    },
    {
      key: 'createdAt',
      header: 'Created',
      render: (wage) => <span className="text-sm text-gray-600">{formatDate(wage.createdAt)}</span>,
    },
  ];

  const wages = earningsData?.data?.wages || [];
  const totalEarnings = earningsData?.data?.totalEarnings || 0;
  const totalDays = earningsData?.data?.totalDays || 0;

  // Create meta object for pagination (since we're getting all data, we'll simulate pagination)
  const meta = {
    total: wages.length,
    lastPage: 1,
    currentPage: 1,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={onBack}
            className="h-10 w-10 bg-transparent"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Header title="Wages Management" description="Manage employee wages and payments" />
        </div>
        <div className="w-[300px]">
          <DatePickerWithRange onDateChange={setDateRange} initialDateRange={dateRange} />
        </div>
      </div>

      {/* Employee Info and Stats Combined Card */}
      <Card className="overflow-hidden py-0">
        <div className="from-primary to-primary/80 bg-gradient-to-r px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <div className="relative">
                <Avatar className="h-20 w-20 border-4 border-white/20">
                  <AvatarImage src={`/placeholder.svg?height=80&width=80`} />
                  <AvatarFallback className="bg-white/20 text-xl font-bold text-white backdrop-blur-sm">
                    {employee.fullName
                      .split(' ')
                      .map((n: string) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -right-1 -bottom-1 flex h-6 w-6 items-center justify-center rounded-full border-2 border-white bg-green-500">
                  <DollarSign className="h-3 w-3 text-white" />
                </div>
              </div>
              <div className="text-white">
                <h3 className="mb-1 text-2xl font-bold">{employee.fullName}</h3>
                <div className="flex items-center gap-4 text-white/80">
                  <span className="flex items-center gap-1">
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    {employee.email}
                  </span>
                  <span className="flex items-center gap-1">
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                    </svg>
                    {employee.phone}
                  </span>
                </div>
                <div className="mt-3">
                  <span className="inline-flex items-center rounded-full bg-white/20 px-3 py-1 text-xs font-medium text-white backdrop-blur-sm">
                    Employee ID: #{employee.id}
                  </span>
                </div>
              </div>
            </div>

            {/* Total Earnings - Right Side */}
            <div className="text-right text-white">
              <div className="rounded-2xl border border-white/20 bg-white/10 px-6 py-4 backdrop-blur-sm">
                <div className="mb-2 flex items-center justify-end gap-2">
                  <span className="text-lg font-medium text-white/80">Total Earnings</span>
                </div>
                <div className="mb-1 text-3xl font-bold text-white">
                  $ {formatCurrency(totalEarnings)}
                </div>
                <div className="text-xs text-white/60">
                  {dateRange?.from && dateRange?.to ? (
                    <>
                      {format(dateRange.from, 'MMM dd')} - {format(dateRange.to, 'MMM dd, yyyy')} •{' '}
                      {wages.length} entries
                    </>
                  ) : (
                    'Select date range'
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats Bar */}
        <div className="bg-gray-50 px-6 py-6">
          <div className="grid grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{wages.length}</div>
              <div className="text-sm text-gray-600">Total Entries</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{totalDays}</div>
              <div className="text-sm text-gray-600">Total Days</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(wages.length > 0 ? totalEarnings / wages.length : 0)}
              </div>
              <div className="text-sm text-gray-600">Average Wage</div>
            </div>
          </div>
        </div>
      </Card>

      {/* Wages List using DataTable */}
      <Card>
        <CardHeader className="">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">Wage History</CardTitle>
              <CardDescription className="text-base">
                Wage entries for {employee.fullName} in the selected date range
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={wages}
            meta={meta}
            onPageChange={() => {}} // No pagination needed for now
            itemsPerPage={wages.length || 10}
            onItemsPerPageChange={() => {}} // No pagination needed for now
            isLoading={isLoading}
            error={error?.message || null}
            emptyState={{
              icon: <DollarSign className="h-12 w-12 text-gray-400" />,
              title: 'No wages found',
              description:
                dateRange?.from && dateRange?.to
                  ? 'No wage entries found for the selected date range.'
                  : 'Select a date range to view wage entries.',
            }}
            getRowKey={(wage) => wage.id}
            className="mt-6"
          />
        </CardContent>
      </Card>
    </div>
  );
};
