import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function SkeletonBankCard() {
  return (
    <Card className="relative overflow-hidden border-0 shadow-md">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-lg p-2" />
            <div className="space-y-2">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Account Holder Skeleton */}
        <div>
          <div className="mb-1 flex items-center justify-between">
            <Skeleton className="h-3 w-24" />
            <Skeleton className="h-5 w-12" />
          </div>
          <Skeleton className="h-5 w-40" />
        </div>

        {/* Verification Details Skeleton */}
        {/* <div className="rounded-lg bg-gradient-to-r from-slate-50 to-slate-100 p-4">
          <Skeleton className="mb-2 h-3 w-28" />
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-20" />
            </div>
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div> */}

        {/* Account Details Skeleton */}
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Skeleton className="mb-1 h-3 w-24" />
            <Skeleton className="h-4 w-36" />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="mb-1 h-3 w-20" />
              <Skeleton className="h-5 w-16" />
            </div>
            <div className="text-right">
              <Skeleton className="mb-1 h-3 w-12" />
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
        </div>

        {/* Status and Verification Skeleton */}
        <div className="flex items-center justify-between pt-2">
          <Skeleton className="h-6 w-16" />
          <div className="flex items-center gap-1">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-3 w-12" />
          </div>
        </div>

        {/* Additional Info Skeleton */}
        <div className="space-y-1 border-t border-slate-100 pt-2">
          <Skeleton className="h-3 w-32" />
          <Skeleton className="h-3 w-40" />
        </div>
      </CardContent>
    </Card>
  );
}
