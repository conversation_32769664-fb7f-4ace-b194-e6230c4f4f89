import { Card, CardContent } from '@/components/ui/card';
import { Check } from 'lucide-react';
import Image from 'next/image';

const AuthLeftLayout = () => {
  return (
    <div className="font-inter flex w-3/6 items-center justify-center bg-gradient-to-br from-[#F74464] to-[#F74464]/80 p-12">
      <div className="max-w-md space-y-8 text-white">
        {/* Logo */}
        <div className="mb-8 flex justify-start">
          <Image
            src="/finwage-auth-logo.png"
            alt="Finwage Logo"
            width={300}
            height={150}
            className="object-contain"
          />
        </div>

        <div className="space-y-3 pl-3">
          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="text-left text-4xl leading-tight font-bold">
              Unlock your salary, <span className="block">whenever you need it.</span>
            </h1>
          </div>

          {/* Description */}
          <div className="space-y-6">
            <p className="text-left text-xl leading-relaxed text-white/90">
              Finwage lets employees access their earned salary anytime—no more waiting for payday
            </p>
          </div>

          {/* Feature List */}
          <div className="mt-12 space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                <Check className="h-4 w-4 text-white" />
              </div>
              <span className="text-lg font-medium text-white">Instant Salary Access</span>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                <Check className="h-4 w-4 text-white" />
              </div>
              <span className="text-lg font-medium text-white">Zero Payroll Hassle</span>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-white/20">
                <Check className="h-4 w-4 text-white" />
              </div>
              <span className="text-lg font-medium text-white">Boost Financial Wellness</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLeftLayout;
